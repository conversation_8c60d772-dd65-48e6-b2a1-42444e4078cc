const express = require('express')
const router = express.Router()
const debtAckContractService = require('../services/debt-ack-contract-service')
const irService = require('../services/ir-service')
const loanAccountService = require('../services/loan-account-service')
const installmentService = require('../services/installment-service')
const aaaService = require('./../utils/aaa')
const common = require('../utils/common')
const { REQUEST_EVENT_NAME } = require('../utils/constant')
const axios = require('axios');
const { activeDebtAckContractFactoring, createDebtAckContractFactoring, createDebtAckContractBizzLimit } = require('../services/bizz-debt-ack-contract-service')
router.post('/v1/mobile-sma/debt-ack-contract/create', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_KUNN
    const payload = req.body
    const response = await debtAckContractService.createDebtAckContractSMA(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    common.log('Error at /v1/mobile-sma/debt-ack-contract/create: ' + e.message, 'error')
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/mobile-sma/debt-ack-contract/active', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_KUNN
    const response = await debtAckContractService.activeDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    common.log('Error at /v1/mobile-sma/debt-ack-contract/active: ' + e.message, 'error')
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/create-factoring', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_KUNN
    const response = await createDebtAckContractFactoring(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/create', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_KUNN
    const response = await debtAckContractService.createDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/active-factoring', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_KUNN
    const response = await activeDebtAckContractFactoring(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/active', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_KUNN
    const response = await debtAckContractService.activeDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/active-backdate', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_BACKDATE_KUNN
    const response = await debtAckContractService.activeBackdateDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/refund-suspend-factoring', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.REFUND_SUSPEND_FACTORING
    const response = await debtAckContractService.refundSuspendFactoringDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/callback-refund-suspend', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CALLBACK_REFUND_SUSPEND
    const response = await debtAckContractService.callbackRefundSuspendDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/transfer-suspend-factoring', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.TRANSFER_SUSPEND_FACTORING
    const response = await debtAckContractService.transferSuspendFactoringDebtAckContract(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v2/debt-ack-contract/active', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_KUNN
    const response = await debtAckContractService.activeDebtAckContractV2(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v2/debt-ack-contract/update-disbur', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_DISBUR
    const response = await debtAckContractService.updateDisburserment(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/cancel', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CANCEL_KUNN
    const payload = req.body
    const response = await loanAccountService.cancelLoanAccount(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/simulation-emi', function (req, res) {
  try {
    const response = installmentService.getEmiSimulation(req.body)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})
router.get('/v1/loan-debt-ack-contract', function (req, res) {
  loanAccountService.getLoanDebtAckContract(req, res)
})
router.post('/v1/debt-ack-contract/cron-job/adjust-interest-rate', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ADJUST_INTEREST_JOB
    const payload = req.body
    const response = await irService.adjustInterestRateBatchJob(payload)
    res.status(response.statusCode).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/debt-ack-contract/update-loan-account', aaaService.authenInternal, async function (req, res) {
  try {
    const payload = req.body
    const response = await loanAccountService.updateLoanAccountService(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/active-insurance', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_INSURANCE
    const payload = req.query
    const response = await debtAckContractService.activeInsurance(payload.debtAckContractNumber)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.get('/v1/debt-ack-contract/detail-insurance', async function (req, res) {
  try {
    const payload = req.query
    const response = await debtAckContractService.getDetailInsurance(payload.debtAckContractNumber)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.get('/v1/debt-ack-contract/disbursements', async function (req, res) {
  try {
    const payload = req.query
    const response = await debtAckContractService.getDisbursements(payload.debtAckContractNumber)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

router.post('/v1/debt-ack-contract/create-insurance-test', async function (req, res) {
  try {
    const payload = req.body;
    const response = await axios.post(
      'https://apiv2test.baominhportal.com/api/v1/Contracts/create_donbaohiem_namvien_evn',
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    res.status(response.status).json(response.data);
  } catch (e) {
    res.status(500).json({ code: 99, message: e.message });
  }
});

router.post('/v3/debt-ack-contract/create', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_KUNN
    const response = await createDebtAckContractBizzLimit(req)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: 99, message: e.message }))
  }
})

module.exports = router
