const moment = require('moment')
const installmentRepo = require('../repositories/installment-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const HashMap = require('hashmap')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const loanAccountRepoV2 = require('../repositories/loan-account-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const installmentDeductionRepo = require('../repositories/promotion-installment-deduction-repo')
const XLSX = require('xlsx')
const lodash = require('lodash')
const productService = require('./../other-services/product-service')
const voucherService = require('./../other-services/voucher-service')
const {
  DATE_FORMAT,
  BILL_ON_DUE,
  ANNEX,
  CONTRACT_TYPE,
  INSTALLMENT,
  CALCUCFG,
  PROMOTION_TYPE,
  IR_PRIN_NORMAL_RATE_TYPE,
  PAYMENT_STATUS,
  FEE,
  config,
    PARTNER_CODE
} = require('../utils/constant')
const common = require('../utils/common')
const holidayService = require('../services/holiday-service')
const { isPrinEveryCycle, isLosUnitedPartnerCode} = require('../utils/helper')
const { LoanAccountRepository } = require('../repositories-v2')
const constant = require("../utils/constant");
const getInsmInfo = async function (pl) {
  try {
    console.log('req query getInsmInfo: ', JSON.stringify(pl))
    if (pl == undefined || pl.debtAckContractNumber == undefined) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Chưa truyen ma khe nuoc nhan no'
      }
    }
    const [rsDataInsm, rsLoanAcc, rsIrCharge, listBillPrin, rsListAnnex, listPromotionDeduction] = await Promise.all([
      installmentRepo.getInsmInfo(global.poolRead, pl),
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, pl),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: pl.debtAckContractNumber,
        irType: 1
      }),
      billOnDueRepo.getAllPrinBillOnDueCompletedPayment(pl.debtAckContractNumber),
      loanAnnexRepo.findAnnexNotCancelByDebtAck(global.poolRead, pl),
      installmentDeductionRepo.findListInstallmentDeduction(pl.debtAckContractNumber)
    ])
    // todo total totalPrinAmount , totalInterestAmount, totalFeeAmount lay tu bang installment
    const listAnnex = rsListAnnex.rows
    const objLoan = rsLoanAcc.rows[0] || {}

    const fetAnnex = listAnnex.find(
      (item) => item.annex_type == ANNEX.TYPE.FULL_EARLY_TERMINATION && item.annex_status == ANNEX.STATUS.DONE
    )
    let originalAmount = Number(objLoan.apr_limit_amt || 0)
    const listDataCycles = processListInstallment(
      rsDataInsm,
      rsIrCharge.rows?.[0]?.ir_value,
      isPrinEveryCycle(objLoan),
      listBillPrin,
      listPromotionDeduction
    )
    let totalInterestAmount = 0
    let totalFeeAmount = 0
    let totalPaid = Number(objLoan.prin_paid || 0) + Number(objLoan.int_paid || 0) + Number(objLoan.fee_paid_amt || 0)
    let lpiAnnex = 0
    let remainingLpiAnnex = 0
    for (const dataCycle of listDataCycles) {
      totalInterestAmount += dataCycle.remainIrAmount
      totalFeeAmount += dataCycle.remainFeeAmount
      totalPaid += dataCycle.paidLpiIr + dataCycle.paidLpiPrin
      lpiAnnex += dataCycle.lpi || 0
      remainingLpiAnnex += dataCycle.remainingLpi || 0
    }

    const financialDetail = {
      financedAmount: Number(objLoan.rls_amt || 0),
      datesManagement: Number(objLoan.tenor || 0),
      invoicingDay: objLoan.bill_day || objLoan.active_date?.getDate(),
      offerTemplate: objLoan.product_code,
      terminationDate: fetAnnex?.termination_date || null,
      totalInstallmentAmount:
        Number(objLoan.prin_amt || 0) +
        Number(objLoan.prin_paid || 0) +
        Number(objLoan.int_amt || 0) +
        Number(objLoan.int_paid || 0),
      totalPrinAmount: Number(objLoan.prin_amt || 0),
      totalInterestAmount,
      totalFeeAmount,
      totalPaid,
      percentage: Math.round(rsIrCharge.rows?.[0]?.ir_value * 10000) / 100,
      lpiAnnex,
      remainingLpiAnnex
    }

    const tenorsAffected = []
    const objOriginalAmountCycle = {}
    let prinMonthy = 0
    for (const billPrin of listBillPrin) {
      if (billPrin.is_annex == BILL_ON_DUE.IS_ANNEX.FALSE) {
        continue
      }
      let billNumCycle = billPrin.num_cycle
      const annexObj = listAnnex.find((item) => item.annex_number == billPrin.annex_number)

      if (
        annexObj &&
        annexObj.annex_type == ANNEX.TYPE.FULL_EARLY_TERMINATION &&
        objLoan.contract_type == CONTRACT_TYPE.CASHLOAN
      ) {
        tenorsAffected.push({
          numCycle: billNumCycle,
          originalAmount: Number(billPrin.amount),
          remainAmount: 0,
          annexNumber: billPrin.annex_number,
          amountAnnex: Number(annexObj.prin_amt),
          terminationDate: annexObj.termination_date
        })
        continue
      }

      if (objLoan.contract_type == CONTRACT_TYPE.CREDITLINE) {
        const findInsm = rsDataInsm.rows.find(
          (item) =>
            item.type == INSTALLMENT.TYPE.INT &&
            item.start_date <= billPrin.on_due_date &&
            item.end_date >= billPrin.on_due_date
        )
        billNumCycle = findInsm.ir_num_cycle
      }
      const findInsmPrin = rsDataInsm.rows.find(
        (item) => item.type == INSTALLMENT.TYPE.PRIN && item.num_cycle == billNumCycle
      )
      if (findInsmPrin) {
        !prinMonthy && (prinMonthy = Number(findInsmPrin.amount))
      }
      if (!objOriginalAmountCycle[billNumCycle] && objLoan.contract_type == CONTRACT_TYPE.CASHLOAN) {
        objOriginalAmountCycle[billNumCycle] = prinMonthy
      }
      const newOriginalAmount = objOriginalAmountCycle[billNumCycle]
        ? objOriginalAmountCycle[billNumCycle] - Number(billPrin.amount)
        : originalAmount - Number(billPrin.amount)
      if (annexObj) {
        tenorsAffected.push({
          numCycle: billNumCycle,
          originalAmount: objOriginalAmountCycle[billNumCycle] || originalAmount,
          remainAmount: newOriginalAmount,
          annexNumber: billPrin.annex_number,
          amountAnnex: Number(annexObj.prin_amt),
          terminationDate: annexObj.termination_date
        })
      }
      objOriginalAmountCycle[billNumCycle] = newOriginalAmount
      originalAmount = newOriginalAmount
    }
    tenorsAffected.sort(function (a, b) {
      return new Date(a.terminationDate) - new Date(b.terminationDate)
    })
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
      financialDetail,
      data: listDataCycles,
      tenorsAffected,
      nonAllocationAmt: Number(objLoan?.non_allocation_amt || 0)
    }
  } catch (error) {
    console.error(error)
    return {
      code: 99,
      message: error.message,
      statusCode: error.statusCode || 500
    }
  }
}
const exportDataInstallment = async function (debtAckContractNumber) {
  try {
    if (!debtAckContractNumber) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Chưa truyen ma khe nuoc nhan no'
      }
    }
    const func = await Promise.all([
      installmentRepo.getInsmInfo(global.poolRead, { debtAckContractNumber }),
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, { debtAckContractNumber }),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, { debtAckContractNumber, irType: 1 }),
      billOnDueRepo.getAllPrinBillOnDueCompletedPayment(debtAckContractNumber),
      installmentDeductionRepo.findListInstallmentDeduction(debtAckContractNumber)
    ])
    const rsData = func[0]
    const rsLoanAcc = func[1]
    const rsIrCharge = func[2]
    const listBillPrin = func[3]
    const listPromotion = func[4]

    const objLoan = rsLoanAcc.rows[0] || {}
    if (rsData.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Khong tim thay du lieu'
      }
    }
    const originalAmount = Number(objLoan.apr_limit_amt || 0)

    const result = processListInstallment(
      rsData,
      rsIrCharge.rows?.[0]?.ir_value,
      isPrinEveryCycle(objLoan),
      listBillPrin,
      listPromotion
    )

    let dataExcel = []

    for (const installment of result) {
      dataExcel = [
        ...dataExcel,
        [
          installment.invoiced,
          moment(installment.dueDate).format(DATE_FORMAT.DDMMYYYY),
          installment.numCycle,
          moment(installment.startDate).format(DATE_FORMAT.DDMMYYYY),
          moment(installment.endDate).format(DATE_FORMAT.DDMMYYYY),
          installment.capitalRefunded,
          installment.interest,
          installment.irCharge,
          installment.feeAmount,
          installment.totalCycle,
          installment.outstandingCapital,
          installment.paidPeriodAmount,
          installment.paidDueCapital,
          installment.paidIrAmount,
          installment.paidFeeAmount,
          installment.remainingDueCapital,
          installment.remainingDueInterest,
          installment.remainFee
        ]
      ]
    }

    const dataFinal = [
      [
        'Invoiced',
        'Due date',
        'Nb',
        'Installment start date',
        'Installment end date',
        'Capital refunded',
        'Interest',
        'Rate',
        'Fee Amount',
        'Total amount',
        'Out standing capital',
        'Paid Period',
        'Paid Principal',
        'Paid Interest',
        'Paid Fee',
        'Remaining due capital',
        'Remaining due interest',
        'Remaining due fee'
      ],
      ...dataExcel
    ]

    const ws = XLSX.utils.aoa_to_sheet(dataFinal)

    const wb = XLSX.utils.book_new()

    XLSX.utils.book_append_sheet(wb, ws, 'INSTALLMENT')

    const fileBuffer = XLSX.write(wb, {
      type: 'buffer',
      bookType: 'xlsx'
    })

    // fs.writeFileSync('test.xlsx', fileBuffer)
    const resultExcel = fileBuffer.toString('base64')

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
      data: resultExcel
    }
  } catch (error) {
    console.error(error)
    return {
      statusCode: 500,
      message: error.message,
      code: 99
    }
  }
}

const getInstallmentInfoMisa = async function (payload) {
  console.log('req query getInsmInfo: ', JSON.stringify(payload))
  if (payload == undefined || payload.debtAckContractNumber == undefined) {
    return {
      statusCode: 400,
      code: 1,
      message: '[LMS-MC] Chưa truyen ma khe nuoc nhan no'
    }
  }
  const [rsDataInsm, rsLoanAcc, rsIrCharge] = await Promise.all([
    installmentRepo.getInsmInfo(global.poolRead, payload),
    loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, payload),
    irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
      debtAckContractNumber: payload.debtAckContractNumber,
      irType: 1
    })
  ])
  const objLoan = rsLoanAcc.rows[0] || {}
  if (rsDataInsm.rowCount == 0) {
    return {
      statusCode: 400,
      code: 1,
      message: '[LMS-MC] Khong tim thay du lieu lich tra no'
    }
  }
  const listDataCycles = processListInstallment(rsDataInsm, rsIrCharge.rows?.[0]?.ir_value, isPrinEveryCycle(objLoan))

  const result = listDataCycles.map((item) => {
    return {
      isInvoiced: item.invoiced,
      numCycle: item.numCycle,
      dueDate: item.dueDate,
      totalCycle: item.dueDate,
      outstandingCapital: item.outstandingCapital,
      prinDue: item.capitalRefunded,
      interestDue: item.interest,
      feeDue: item.fee
    }
  })
  return {
    statusCode: 200,
    code: 0,
    data: {
      listInstallment: result,
      remainPeriod: `${result.filter((item) => item.isInvoiced == 0).length}/${result.length}`
    }
  }
}
function processListInstallment(
  rsData,
  intValue = 0,
  priEveryCycle = 0,
  listBillPrin = [],
  listPromotionDeduction = []
) {
  if (priEveryCycle == 1) {
    return processListInstallmentCashLoan(rsData, intValue, listBillPrin, listPromotionDeduction)
  }
  return processListInstallmentCreditline(rsData, intValue, listBillPrin)
}

function processListInstallmentCreditline(rsData, intValue = 0, listBillPrin = []) {
  const hmCycle = new HashMap()
  const result = []
  let totalOutstading = 0
  let maxIrNumCycle = 0
  let objPrin = null
  for (const i in rsData.rows) {
    const dataObj = rsData.rows[i]
    if (dataObj.ir_num_cycle) {
      maxIrNumCycle = Math.max(maxIrNumCycle, dataObj.ir_num_cycle)
      const mapValue = hmCycle.get(dataObj.ir_num_cycle)
      if (mapValue != undefined) {
        mapValue.push(dataObj)
        hmCycle.set(dataObj.ir_num_cycle, mapValue)
      } else {
        hmCycle.set(dataObj.ir_num_cycle, [dataObj])
      }
    }
    if (dataObj.type == 1) {
      objPrin = dataObj
    }
  }
  if (objPrin) {
    totalOutstading += Number(objPrin.amount)
  }
  const mapToArray = Array.from(hmCycle.values())

  for (let i = 0; i < mapToArray.length; i++) {
    const mapObj = mapToArray[i]
    const obj = {
      irCharge: Math.round(intValue * 10000) / 100,
      remainIrAmount: 0,
      remainFee: 0,
      remainLpiPrin: 0,
      remainLpiIr: 0,
      paidLpiPrin: 0,
      paidLpiIr: 0,
      capitalRefunded: 0,
      remainPrinAmount: 0,
      remainingDueCapital: 0,
      remainingDueInterest: 0,
      paidIrAmount: 0,
      paidPeriodAmount: 0,
      paidFeeAmount: 0,
      paidDueCapital: 0,
      annexEffected: false,
      detailInterest: [],
      fee: 0,
      interest: 0,
      feeAmount: 0,
      penaltyFeeAmount: 0,
      remainFeeAmount: 0,
      feeAmountTex: 0,
      prinMonthy: 0,
      outstandingCapital: 0,
      invoiced: INSTALLMENT.CLOSE.TRUE,
      lpi: 0,
      remainingLpi: 0
    }

    let maxOutStandingCapital = null
    let minOutStandingCapital = null
    for (const j in mapObj) {
      if (mapObj[j].is_annex == 1) {
        obj.annexEffected = true
      }

      obj.numCycle = mapObj[j].num_cycle

      if (mapObj[j].closed == INSTALLMENT.CLOSE.FALSE) {
        obj.invoiced = mapObj[j].closed
      }

      !obj.irNumCycle && (obj.irNumCycle = mapObj[j].ir_num_cycle)

      if (mapObj[j].type == 1) {
        obj.dueDate = mapObj[j].due_date
        obj.startDate = mapObj[j].start_date
        obj.endDate = mapObj[j].end_date
        obj.capitalRefunded = Number(mapObj[j].amount)
        obj.remainingDueCapital = Number(mapObj[j].remain_amount)
        obj.paidDueCapital = Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        totalOutstading -= Number(mapObj[j].amount)
      }
      if (mapObj[j].type == 2) {
        obj.irCharge = mapObj[j].ir_rate ? Math.round(mapObj[j].ir_rate * 10000) / 100: obj.irCharge
        obj.dueDate = mapObj[j].due_date
        obj.startDate = mapObj[j].start_date
        obj.endDate = mapObj[j].end_date
        obj.instalId = mapObj[j].id
        const outStandingCap = Number(mapObj[j].outstanding_prin) || totalOutstading
        // console.log('outStandingCap', outStandingCap);
        obj.outstandingCapital = obj.outstandingCapital
          ? Math.min(obj.outstandingCapital, outStandingCap)
          : outStandingCap

        maxOutStandingCapital =
          maxOutStandingCapital != null ? Math.max(maxOutStandingCapital, outStandingCap) : obj.outstandingCapital
        minOutStandingCapital =
          minOutStandingCapital != null ? Math.min(minOutStandingCapital, outStandingCap) : obj.outstandingCapital

        obj.remainIrAmount += Number(mapObj[j].remain_amount)
        obj.interest += Number(mapObj[j].amount)
        obj.remainingDueInterest += Number(mapObj[j].remain_amount)
        obj.feeAmountVat = 0
        obj.irFromDate = mapObj[j].start_date
        obj.irToDate = mapObj[j].end_date
        obj.paidIrAmount += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        obj.detailInterest.push({
          prinAmount: Number(mapObj[j].outstanding_prin) || obj.outstandingCapital,
          irRate: Math.round(Number(mapObj[j].ir_rate * 100) || intValue * 100),
          irFromDate: mapObj[j].ir_from_date || mapObj[j].start_date,
          irToDate: mapObj[j].ir_to_date || mapObj[j].end_date,
          interest: Number(mapObj[j].amount)
        })
      }
      if (mapObj[j].type == 5) {
        if (mapObj[j].is_annex == 0) {
          obj.remainFee += mapObj[j].closed == 1 ? Number(mapObj[j].remain_amount) : 0
          obj.fee += Number(mapObj[j].amount)
          obj.feeAmountTex += Number(mapObj[j].amount)
          obj.feeAmount += Number(mapObj[j].amount)
          obj.remainFeeAmount += Number(mapObj[j].remain_amount)
          obj.paidFeeAmount += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        } else {
          obj.penaltyFeeAmount += Number(mapObj[j].amount)
        }
      }
      if (mapObj[j].type == 3) {
        obj.remainLpiPrin += Number(mapObj[j].remain_amount)
        obj.paidLpiPrin += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
      }
      if (mapObj[j].type == 4) {
        obj.remainLpiIr += Number(mapObj[j].remain_amount)
        obj.paidLpiIr += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
      }
      if ([INSTALLMENT.TYPE.LPI_INT, INSTALLMENT.TYPE.LPI_PRIN].includes(mapObj[j].type)) {
        obj.lpi += Number(mapObj[j].amount)
        obj.remainingLpi += Number(mapObj[j].remain_amount)
      }
      !obj.outstandingCapital && (obj.outstandingCapital = Number(mapObj[j].outstanding_prin))
    }
    obj.capitalRefunded = maxOutStandingCapital - minOutStandingCapital

    const listBill = listBillPrin.filter((item) => item.on_due_date < obj.endDate && item.on_due_date >= obj.startDate)
    if (listBill.length) {
      obj.capitalRefunded = 0
      for (const bill of listBill) {
        obj.capitalRefunded += Number(bill.amount)
      }
    }
    obj.paidDueCapital = obj.capitalRefunded
    obj.remainPrinAmount = obj.outstandingCapital
    if (i == mapToArray.length - 1) {
      if (!listBill.length) {
        obj.capitalRefunded = obj.outstandingCapital
        obj.remainingDueCapital = Number(objPrin.remain_amount)
      } else {
        obj.outstandingCapital = 0
        for (const bill of listBill) {
          obj.outstandingCapital += Number(bill.amount)
        }
      }
      obj.paidDueCapital = obj.outstandingCapital - obj.remainingDueCapital
      obj.remainPrinAmount = 0
    }

    obj.numCycle = obj.irNumCycle || obj.numCycle

    obj.paidPeriodAmount = obj.paidDueCapital + obj.paidIrAmount + obj.paidFeeAmount

    obj.totalCycle = obj.capitalRefunded + obj.interest + obj.feeAmount

    obj.paymentStatus = 0
    if (!obj.invoiced) {
      obj.remainingDueCapital = null
      obj.remainingDueInterest = null
      obj.remainFee = null

      if (!obj.annexEffected) {
        obj.paidDueCapital = null
        obj.paidIrAmount = null
        obj.paidFeeAmount = null
        obj.paidPeriodAmount = null
      }
    } else {
      if (!obj.remainingDueInterest && !obj.remainingDueCapital && !obj.remainFee) {
        obj.paymentStatus = 1
      }
    }

    obj.detailInterest.sort(function (a, b) {
      return a.irFromDate - b.irFromDate
    })
    const compareDueDateEndDate = obj.dueDate.getTime() == obj.endDate.getTime()
    let upcomingDays = common.getDifferencesDays(
      moment().format(DATE_FORMAT.YYYYMMDD2),
      moment(obj.dueDate).format(DATE_FORMAT.YYYYMMDD2),
      false
    )
    !compareDueDateEndDate && (upcomingDays += 1)
    obj.status = upcomingDays <= 0 ? 'upcoming' : 'overdue'
    result.push(obj)
    console.log(obj)
  }
  return result
}

function processListInstallmentCashLoan(rsData, intValue = 0, listBillPrin = [], listPromotionDeduction = []) {
  const hmCycle = new HashMap()
  const result = []
  let totalOutstading = 0
  for (const i in rsData.rows) {
    const dataObj = rsData.rows[i]
    if (dataObj.num_cycle) {
      const mapValue = hmCycle.get(dataObj.num_cycle)
      if (mapValue != undefined) {
        mapValue.push(dataObj)
        hmCycle.set(dataObj.num_cycle, mapValue)
      } else {
        hmCycle.set(dataObj.num_cycle, [dataObj])
      }
    }
    if (dataObj.type == 1) totalOutstading += Number(dataObj.amount)
  }
  const mapToArray = Array.from(hmCycle.values())

  for (let i = 0; i < mapToArray.length; i++) {
    const mapObj = mapToArray[i]
    const obj = {
      irCharge: Math.round(intValue * 10000) / 100,
      remainIrAmount: 0,
      remainFee: 0,
      remainLpiPrin: 0,
      remainLpiIr: 0,
      paidLpiPrin: 0,
      paidLpiIr: 0,
      capitalRefunded: 0,
      remainPrinAmount: 0,
      remainingDueCapital: 0,
      remainingDueInterest: 0,
      paidIrAmount: 0,
      paidPeriodAmount: 0,
      paidFeeAmount: 0,
      paidDueCapital: 0,
      annexEffected: false,
      detailInterest: [],
      fee: 0,
      interest: 0,
      feeAmount: 0,
      penaltyFeeAmount: 0,
      remainFeeAmount: 0,
      feeAmountTex: 0,
      originalAmount: 0,
      deductionAmount: 0,
      prinMonthy: 0,
      outstandingCapital: 0,
      invoiced: INSTALLMENT.CLOSE.TRUE,
      lpi: 0,
      remainingLpi: 0
    }

    let maxOutStandingCapital = null
    let minOutStandingCapital = null
    for (const j in mapObj) {
      if (mapObj[j].is_annex == 1) {
        obj.annexEffected = true
      }

      obj.numCycle = mapObj[j].num_cycle

      if (mapObj[j].closed == 0) {
        obj.invoiced = mapObj[j].closed
      }

      !obj.irNumCycle && (obj.irNumCycle = mapObj[j].ir_num_cycle)

      if (mapObj[j].type == 1) {
        obj.dueDate = mapObj[j].due_date
        obj.startDate = obj.startDate ? Math.min(obj.startDate, mapObj[j].start_date) : mapObj[j].start_date
        obj.endDate = mapObj[j].end_date
        obj.capitalRefunded += Number(mapObj[j].amount)
        obj.remainingDueCapital += Number(mapObj[j].remain_amount)
        obj.paidDueCapital += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        totalOutstading -= Number(mapObj[j].amount)
        obj.prinMonthy += Number(mapObj[j].amount)
      }
      if (mapObj[j].type == 2) {
        obj.instalId = mapObj[j].id
        const outStandingCap = Number(mapObj[j].outstanding_prin) || totalOutstading
        obj.outstandingCapital = obj.outstandingCapital
          ? Math.min(obj.outstandingCapital, outStandingCap)
          : outStandingCap

        maxOutStandingCapital =
          maxOutStandingCapital != null ? Math.max(maxOutStandingCapital, outStandingCap) : obj.outstandingCapital
        minOutStandingCapital =
          minOutStandingCapital != null ? Math.min(minOutStandingCapital, outStandingCap) : obj.outstandingCapital

        obj.remainIrAmount += Number(mapObj[j].remain_amount)
        obj.interest += Number(mapObj[j].amount)
        obj.remainingDueInterest += Number(mapObj[j].remain_amount)
        obj.feeAmountVat = 0
        obj.irFromDate = mapObj[j].start_date
        obj.irToDate = mapObj[j].end_date
        obj.paidIrAmount += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        obj.detailInterest.push({
          prinAmount: Number(mapObj[j].outstanding_prin) || obj.outstandingCapital,
          irRate: Math.round(Number(mapObj[j].ir_rate * 100) || intValue * 100),
          irFromDate: mapObj[j].ir_from_date || mapObj[j].start_date,
          irToDate: mapObj[j].ir_to_date || mapObj[j].end_date,
          interest: Number(mapObj[j].amount)
        })
      }
      if (mapObj[j].type == 5) {
        if (mapObj[j].is_annex == 0) {
          obj.remainFee += mapObj[j].closed == 1 ? Number(mapObj[j].remain_amount) : 0
          obj.fee += Number(mapObj[j].amount)
          obj.feeAmountTex += Number(mapObj[j].amount)
          obj.feeAmount += Number(mapObj[j].amount)
          obj.remainFeeAmount += Number(mapObj[j].remain_amount)
          obj.paidFeeAmount += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
        } else {
          obj.penaltyFeeAmount += Number(mapObj[j].amount)
        }
      }
      if (mapObj[j].type == 3) {
        obj.remainLpiPrin += Number(mapObj[j].remain_amount)
        obj.paidLpiPrin += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
      }
      if (mapObj[j].type == 4) {
        obj.remainLpiIr += Number(mapObj[j].remain_amount)
        obj.paidLpiIr += Number(mapObj[j].amount) - Number(mapObj[j].remain_amount)
      }
      if ([INSTALLMENT.TYPE.LPI_INT, INSTALLMENT.TYPE.LPI_PRIN].includes(mapObj[j].type)) {
        obj.lpi += Number(mapObj[j].amount)
        obj.remainingLpi += Number(mapObj[j].remain_amount)
      }
      if (listPromotionDeduction.length && [INSTALLMENT.TYPE.INT, INSTALLMENT.TYPE.FEE].includes(mapObj[j].type)) {
        const checkPromotionDeduction = listPromotionDeduction.find((item) => item.installment_id == mapObj[j].id)
        if (checkPromotionDeduction) {
          obj.originalAmount += Number(checkPromotionDeduction.original_amount)
          obj.deductionAmount += Number(checkPromotionDeduction.deduction_amount)
        }
      }
      !obj.outstandingCapital && (obj.outstandingCapital = Number(mapObj[j].outstanding_prin))
    }
    obj.remainPrinAmount = obj.annexEffected ? obj.outstandingCapital : obj.outstandingCapital - obj.capitalRefunded
    obj.numCycle = obj.irNumCycle || obj.numCycle

    // if (i == mapToArray.length - 1) {
    //   const listBill = listBillPrin.filter(item => item.on_due_date <= obj.endDate && item.on_due_date >= obj.startDate && item.is_annex == 1)
    //   if (listBill.length) {
    //     obj.capitalRefunded = 0
    //     obj.outstandingCapital = 0
    //     for (const bill of listBill) {
    //       obj.capitalRefunded += Number(bill.amount)
    //       obj.outstandingCapital += Number(bill.amount)
    //     }
    //   } else {
    //     obj.capitalRefunded = maxOutStandingCapital
    //     obj.outstandingCapital = maxOutStandingCapital
    //   }
    //   obj.paidDueCapital = obj.outstandingCapital - obj.remainingDueCapital
    //   obj.remainPrinAmount = 0
    // }
    obj.paidPeriodAmount = obj.paidDueCapital + obj.paidIrAmount + obj.paidFeeAmount
    !obj.originalAmount && (obj.originalAmount = obj.interest)
    obj.totalCycle = obj.capitalRefunded + obj.interest + obj.feeAmount
    !obj.outstandingCapital && (obj.outstandingCapital = obj.capitalRefunded)

    obj.paymentStatus = 0
    if (!obj.invoiced) {
      obj.remainingDueCapital = null
      obj.remainingDueInterest = null
      obj.remainFee = null

      if (!obj.annexEffected) {
        obj.paidDueCapital = null
        obj.paidIrAmount = null
        obj.paidFeeAmount = null
        obj.paidPeriodAmount = null
      }
    } else {
      if (!obj.remainingDueInterest && !obj.remainingDueCapital && !obj.remainFee) {
        obj.paymentStatus = 1
      }
    }
    obj.detailInterest.sort(function (a, b) {
      return a.irFromDate - b.irFromDate
    })
    const compareDueDateEndDate = obj.dueDate.getTime() == obj.endDate.getTime()
    let upcomingDays = common.getDifferencesDays(
      moment().format(DATE_FORMAT.YYYYMMDD2),
      moment(obj.dueDate).format(DATE_FORMAT.YYYYMMDD2),
      false
    )
    !compareDueDateEndDate && (upcomingDays += 1)
    obj.status = upcomingDays <= 0 ? 'upcoming' : 'overdue'
    result.push(obj)
  }
  return result
}

async function getNextInsmInfoByContractNumber(contractNumber, infoMonth) {
  if (!contractNumber || !infoMonth) {
    return {
      code: 1,
      statusCode: 400,
      message: 'Missing input contractNumber, infoMonth'
    }
  }
  const listLoanAcc = await loanAccountRepoV2.findListContractByContractNumber(contractNumber)
  const result = []
  const sumData = {
    sumTotalCycle: 0,
    sumCapitalRefunded: 0,
    sumInterest: 0,
    sumFeeAmount: 0
  }
  await Promise.all(
    listLoanAcc.map(async (item) => {
      const installmentData = await getInsmInfo({ debtAckContractNumber: item.debt_ack_contract_number })
      const firstInsm = installmentData.data.find((e) => moment(e.endDate).format(DATE_FORMAT.MMYYYY) == infoMonth)
      if (firstInsm) {
        result.push({
          ...firstInsm,
          debtAckContractNumber: item.debt_ack_contract_number
        })
        sumData.sumCapitalRefunded += firstInsm.capitalRefunded
        sumData.sumInterest += firstInsm.interest
        sumData.sumFeeAmount += firstInsm.feeAmount
        sumData.sumTotalCycle += firstInsm.feeAmount + firstInsm.capitalRefunded + firstInsm.interest
      }
    })
  )
  return {
    code: 0,
    statusCode: 200,
    message: 'Get upcoming installment successfully',
    sumData,
    result
  }
}

async function getHistoryPaidSme(payload) {
  const dataInstallment = await getInsmInfo(payload)
  const listInstallment = dataInstallment.data
  const listHistoryPaid = []
  let sumPayment = 0
  for (const installment of listInstallment) {
    if (installment.paymentStatus) {
      listHistoryPaid.push({
        startDate: installment.startDate,
        endDate: installment.endDate,
        dueDate: installment.dueDate,
        paidPeriodAmount: installment.paidPeriodAmount,
        paidIrAmount: installment.paidIrAmount,
        interest: installment.interest,
        penaltyFeeAmount: installment.penaltyFeeAmount,
        paidFeeAmount: installment.paidFeeAmount,
        outstandingCapital: installment.outstandingCapital
      })
      sumPayment += installment.paidPeriodAmount
    }
  }
  return {
    code: 0,
    statusCode: 200,
    message: 'GET HISTORY PAID SUCCESSFULLY',
    data: {
      numOfCyclePaid: `${listHistoryPaid.length}/${listInstallment.length}`,
      sumPayment,
      listHistoryPaid
    }
  }
}
function getEmiSimulation(payload) {
  !payload.periodicity && (payload.periodicity = 1)
  const {
    startDate,
    tenor,
    interestRate,
    periodicity,
    graceDayNumber,
    aprLimitAmt,
    promotionAmount = 0,
    listPromotionDeduction = [],
    isAnnex = false
  } = payload
  const calcuCfg = CALCUCFG
  let tempStartDate = new Date(startDate)
  const irRate = common.roundV2(interestRate / calcuCfg.totalDayOfYear, 9)
  let annuityFactor = 0.0
  let product = 1.0
  const listPeriod = []
  for (let numCycle = 1; numCycle <= tenor; numCycle++) {
    let billDate = new Date(tempStartDate)
    if (numCycle == 1 && !isAnnex) {
      const startDay = billDate.getDate()
      const tenorConfigData = global.tenorConfig[startDay]
      let endDay = startDay
      let deltaMonth = 1
      if (tenorConfigData && tenor > 1) {
        endDay = tenorConfigData.endDay
        deltaMonth = tenorConfigData.deltaMonth
      }
      billDate = common.getFirstNextTenor(billDate, endDay, deltaMonth, tenor)
    } else {
      billDate.setMonth(billDate.getMonth() + periodicity)
    }
    const formatStartDate = moment(tempStartDate).format(DATE_FORMAT.YYYYMMDD2)
    const numDayOfTenor = common.getDifferencesDays(moment(billDate).format(DATE_FORMAT.YYYYMMDD2), formatStartDate)

    if (numCycle == tenor || isAnnex) {
      billDate = holidayService.getNextDayAfterHoliday(billDate)
    }
    const tempEndDate = moment(billDate).format(DATE_FORMAT.YYYYMMDD2)
    const tempDueDate = moment(billDate)
    tempDueDate.add(graceDayNumber, 'day')
    listPeriod.push({
      startDate: formatStartDate,
      endDate: tempEndDate,
      dueDate: tempDueDate.format(DATE_FORMAT.YYYYMMDD2),
      numCycle,
      numDayOfTenor
    })
    const rdj = 1.0 + numDayOfTenor * irRate
    product = product / rdj
    annuityFactor += product
    tempStartDate = billDate
  }

  const annuity = common.roundUp(aprLimitAmt / annuityFactor, calcuCfg.scale)
  let sumRemainPrinAmt = aprLimitAmt
  let totalPrinAmt = 0
  let irAmount = 0
  let sumOriginalIrAmount = 0
  let sumIrAmount = 0
  let promotionDeductionAmount = promotionAmount
  let numCycleOfPromotion = 0
  for (const period of listPeriod) {
    irAmount = common.roundV2(irRate * sumRemainPrinAmt * period.numDayOfTenor, calcuCfg.scale)
    period.originalIrAmount = irAmount
    let printAmount = annuity - irAmount
    if (period.numCycle == tenor) {
      printAmount = sumRemainPrinAmt
    }
    if (promotionDeductionAmount) {
      numCycleOfPromotion += 1
      const deductionAmount = Math.min(irAmount, promotionDeductionAmount)
      irAmount -= deductionAmount
      promotionDeductionAmount -= deductionAmount
      period.deductionAmount = deductionAmount
    }
    period.irAmount = irAmount
    period.irRate = interestRate
    period.prinAmount = printAmount
    period.sumRemainPrinAmt = sumRemainPrinAmt
    sumOriginalIrAmount += period.originalIrAmount
    sumIrAmount += period.irAmount
    totalPrinAmt += printAmount
    sumRemainPrinAmt = aprLimitAmt - totalPrinAmt
  }
  const revenueAmount = promotionDeductionAmount

  return {
    code: 0,
    statusCode: 200,
    emi: annuity,
    data: {
      listPeriod,
      sumOriginalIrAmount,
      sumIrAmount,
      numCycleOfPromotion,
      revenueAmount
    }
  }
}

function getInstallmentSimulationMerchant(payload) {
  const {
    startDate,
    tenor,
    interestRate,
    periodicity = 1,
    billDay,
    contractType,
    graceDayNumber,
    aprLimitAmt
  } = payload
  const endDate = common.calNextCycleDateV3(tenor, startDate)

  const loanAccount = {
    periodicity,
    start_date: common.formatDate({ date: startDate }),
    end_date: endDate,
    bill_day: billDay,
    grace_day_number: graceDayNumber,
    tenor,
    apr_limit_amt: aprLimitAmt
  }
  const irChargePrinObj = {
    ir_value: interestRate
  }
  let listInstallment = []
  if (contractType == CONTRACT_TYPE.CASHLOAN) {
    listInstallment = calCycleInstallmentV2(loanAccount, irChargePrinObj)
  } else {
    listInstallment = calCycleInstallmentCreditLine(loanAccount, irChargePrinObj)
  }
  listInstallment = listInstallment.map((arr) => ({
    num_cycle: arr[1],
    amount: arr[2],
    type: arr[5],
    start_date: common.formatDate({ date: arr[12] }),
    end_date: common.formatDate({ date: arr[13] }),
    due_date: common.formatDate({ date: arr[14] }),
    ir_num_cycle: arr[17]
  }))

  const groupByEndDate = lodash.groupBy(listInstallment, 'end_date')
  const result = []
  for (const endDate of Object.keys(groupByEndDate).sort()) {
    const obj = {
      prin_amount: 0,
      int_amount: 0,
      fee_amount: 0
    }
    const listData = groupByEndDate[endDate]
    for (const data of listData) {
      if (data.type == INSTALLMENT.TYPE.INT) {
        obj.start_date = data.start_date
        obj.num_day = common.getDifferencesDays(data.start_date, data.end_date)
        obj.end_date = data.end_date
        obj.due_date = data.due_date
        obj.num_cycle = data.ir_num_cycle
        obj.int_amount = data.amount
      } else if (data.type == INSTALLMENT.TYPE.PRIN) {
        obj.prin_amount = data.amount
      } else if (data.type == INSTALLMENT.TYPE.FEE) {
        obj.fee_amount = data.amount
      }
    }
    result.push(obj)
  }
  return result
}
const simulationVoucherInstallment = async function (payload) {
  const { promotionCode, voucherCode, productCode } = payload
  const startDate = moment().format(DATE_FORMAT.YYYYMMDD2)
  if (!voucherCode || !productCode) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Voucher code or product is required'
    }
  }
  const [promotionData, voucherData, productData] = await Promise.all([
    productService.getVoucherPromotion(promotionCode),
    voucherService.getVoucherInfo(voucherCode),
    productService.getProduct(productCode)
  ])

  if (!voucherData || !voucherData?.voucher) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Voucher not found'
    }
  }
  if (!productData || !productData.rate) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Product not found'
    }
  }
  const { amount: voucherAmount, tenor } = voucherData.voucher
  const interestRate = productData.rate.find((item) => item.rateType == IR_PRIN_NORMAL_RATE_TYPE).intRateVal / 100
  const listPromotionDeduction = []
  let promotionValue = 0
  if (promotionData && promotionData.promotion) {
    const { promotion, promotionInstallment } = promotionData
    promotionValue = promotion?.promotionValue || 0
    if (promotion.promotionType == PROMOTION_TYPE.RATE) {
      promotionValue = Math.min((promotionValue * voucherAmount) / 100, promotion?.promotionMaxValue)
    }
    // promotionInstallment.forEach((item) => {
    //   const deductionAmount = common.roundUp(item.discountRate / 100 * Number(promotionValue), CALCUCFG.scaleVoucher)
    //   listPromotionDeduction.push({
    //     num_cycle: item.numCycle,
    //     deduction_amount: deductionAmount
    //   })
    // })
  }
  const { data } = getEmiSimulation({
    startDate,
    tenor,
    interestRate,
    aprLimitAmt: voucherAmount,
    promotionAmount: promotionValue,
    listPromotionDeduction
  })
  return {
    statusCode: 200,
    code: 0,
    data: {
      voucherAmount,
      tenor,
      prinAmount: voucherAmount,
      originalIrAmount: data.sumOriginalIrAmount,
      irAmount: data.sumIrAmount,
      interestRate,
      numCycleOfPromotion: data.numCycleOfPromotion,
      totalAmount: voucherAmount + data.sumIrAmount,
      detailInstallment: data.listPeriod
    }
  }
}
async function getInstallmentSMA(pl) {
  try {
    common.log('req getInstallmentSMA payload: ' + JSON.stringify(pl))
    if (!pl || !pl.contractNumber) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Input contractNumber is required'
      }
    }
    const [rsDataInsm, rsLoanAcc, rsIrCharge, listBillPrin, rsListAnnex] = await Promise.all([
      installmentRepo.getInsmInfo(global.poolRead, { debtAckContractNumber: pl.contractNumber }),
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, { debtAckContractNumber: pl.contractNumber }),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: pl.contractNumber,
        irType: 1
      }),
      billOnDueRepo.getAllPrinBillOnDueCompletedPayment(pl.contractNumber),
      loanAnnexRepo.findAnnexNotCancelByDebtAck(global.poolRead, { debtAckContractNumber: pl.contractNumber })
    ])
    if (rsLoanAcc.rowCount == 0) {
      return {
        statusCode: 200,
        code: 2,
        message: '[LMS-MC] contractNumber not found'
      }
    }
    const objLoan = rsLoanAcc.rows[0] || {}
    const listDataCycles = processListInstallment(
      rsDataInsm,
      rsIrCharge.rows?.[0]?.ir_value,
      objLoan.tenor == objLoan.periodicity ? 0 : 1,
      listBillPrin
    )
    const resultObj = {
      totalPaidAmt: Number(objLoan.prin_paid || 0) + Number(objLoan.int_paid || 0) + Number(objLoan.fee_paid_amt || 0),
      totalPaidTenor: 0,
      totalAmt: 0,
      totalTenor: objLoan.tenor
    }
    const insList = []
    for (const objData of listDataCycles) {
      resultObj.totalAmt +=
        Number(objData.capitalRefunded || 0) + Number(objData.interest || 0) + Number(objData.fee || 0)
      if (objData.invoiced == 1) {
        resultObj.totalPaidTenor =
          resultObj.totalPaidTenor < objData.numCycle ? objData.numCycle : resultObj.totalPaidTenor
      }
      insList.push({
        nextCycle: objData.endDate ? moment(objData.endDate).format(DATE_FORMAT.YYYYMMDD2) : null,
        amount: Number(objData.capitalRefunded || 0) + Number(objData.interest || 0) + Number(objData.fee || 0)
      })
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
      data: {
        loanInfo: resultObj,
        installmentList: insList
      }
    }
  } catch (error) {
    console.log('Error at getInstallmentSMA:', error.message)
    return {
      code: 99,
      message: error.message,
      statusCode: error.statusCode || 500
    }
  }
}
const calCycleInstallmentCreditLine = function (loanAccount, irChargePrinObj, amortId) {
  const calcuCfg = CALCUCFG
  const payload = config
  const arrResult = []
  let amountMonthy = common.roundUp(
    (loanAccount.apr_limit_amt / loanAccount.tenor) * loanAccount.periodicity,
    calcuCfg.scale
  )
  const firstDay = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let nextDay = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let nextDayMonthy = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let startDate, endDate, dueDate, irStartDate, irEndDate, irDueDate, tempStartDay, tempStartMonthyDay
  let sumRemainPrinAmt = 0
  let totalCycle = loanAccount.tenor / loanAccount.periodicity
  const tempFirstDay = firstDay.split('-')[2]

  if (loanAccount.partner_code === PARTNER_CODE.DNSE) {
    loanAccount.bill_day = tempFirstDay
  }

  if (tempFirstDay != loanAccount.bill_day && totalCycle > 1) {
    totalCycle++
  }

  let irNumCycle = 1
  let totalPriAmt = 0
  for (let i = 0; i < totalCycle; i++) {
    tempStartDay = nextDay.split('-')[2]
    startDate = nextDay
    if (tempStartDay > loanAccount.bill_day && i == 0 && totalCycle > 1) {
      const tempDate = new Date(nextDay)
      tempDate.setDate(loanAccount.bill_day)
      nextDay = common.calNextCycleDate2(loanAccount.periodicity, common.convertDatetoString(tempDate, 'yyyy-mm-dd'))
      sumRemainPrinAmt = loanAccount.apr_limit_amt
    } else if (tempStartDay < loanAccount.bill_day && i == 0 && totalCycle > 1) {
      const tempDate = new Date(nextDay)
      tempDate.setDate(loanAccount.bill_day)
      nextDay = common.convertDatetoString(tempDate, 'yyyy-mm-dd')
    } else if (tempFirstDay > loanAccount.bill_day && i == totalCycle - 1 && totalCycle > 1) {
      const tempDate = common.getDayOfMonth(nextDay, tempFirstDay)
      nextDay = tempDate
    } else if (tempFirstDay < loanAccount.bill_day && i == totalCycle - 1 && totalCycle > 1) {
      const tempDate = new Date(nextDay)
      tempDate.setDate(tempFirstDay)
      nextDay = common.calNextCycleDate2(loanAccount.periodicity, common.convertDatetoString(tempDate, 'yyyy-mm-dd'))
    } else {
      nextDay = common.calNextCycleDate2(loanAccount.periodicity, nextDay)
    }
    endDate = nextDay
    dueDate = common.convertDatetoString(common.addDays(endDate, loanAccount.grace_day_number), 'yyyy-mm-dd')
    if (tempFirstDay != loanAccount.bill_day && totalCycle > 1) {
      if (i == 0) {
        sumRemainPrinAmt = loanAccount.apr_limit_amt
        arrResult.push([
          loanAccount.loan_id,
          i + 1,
          0,
          0,
          nextDay,
          1,
          0,
          payload.ownerId,
          payload.isTesting,
          payload.createdBy,
          loanAccount.contract_number,
          loanAccount.debt_ack_contract_number,
          startDate,
          nextDay,
          dueDate,
          i + 1,
          undefined,
          undefined,
          sumRemainPrinAmt,
          undefined,
          undefined,
          amortId,
          undefined
        ])
      } else {
        if (i == totalCycle - 1) amountMonthy = loanAccount.apr_limit_amt - totalPriAmt
        sumRemainPrinAmt = common.roundUp(loanAccount.apr_limit_amt - amountMonthy * (i - 1), calcuCfg.scale)
        arrResult.push([
          loanAccount.loan_id,
          i + 1,
          amountMonthy,
          amountMonthy,
          nextDay,
          1,
          1,
          payload.ownerId,
          payload.isTesting,
          payload.createdBy,
          loanAccount.contract_number,
          loanAccount.debt_ack_contract_number,
          startDate,
          nextDay,
          dueDate,
          i + 1,
          undefined,
          undefined,
          sumRemainPrinAmt,
          undefined,
          undefined,
          amortId,
          undefined
        ])
        totalPriAmt += amountMonthy
      }
    } else {
      if (i == totalCycle - 1) amountMonthy = loanAccount.apr_limit_amt - totalPriAmt
      sumRemainPrinAmt = common.roundUp(loanAccount.apr_limit_amt - amountMonthy * i, calcuCfg.scale)
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        amountMonthy,
        amountMonthy,
        nextDay,
        1,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        startDate,
        nextDay,
        dueDate,
        i + 1,
        undefined,
        undefined,
        sumRemainPrinAmt,
        undefined,
        undefined,
        amortId,
        undefined
      ])
      totalPriAmt += amountMonthy
    }

    // tinh lai hang thang
    let totalCycleMonth = loanAccount.periodicity
    if (i == 0 && tempFirstDay != loanAccount.bill_day && totalCycle == 1) {
      totalCycleMonth = loanAccount.periodicity + 1
    }
    if (i == totalCycle - 1 && tempFirstDay != loanAccount.bill_day && totalCycle > 1) {
      totalCycleMonth = 1
    }

    // them ban ghi lai va phi khi khoi tao istallment
    let irAmount = 0
    for (let j = 0; j < totalCycleMonth; j++) {
      tempStartMonthyDay = nextDayMonthy.split('-')[2]
      irStartDate = nextDayMonthy

      if (i == 0 && j == 0 && loanAccount.partner_code === PARTNER_CODE.DNSE) {
        const startDay = new Date(irStartDate).getDate()
        const tenorConfigData = global.tenorConfig[startDay]
        let endDay = startDay
        let deltaMonth = 1
        if (tenorConfigData && loanAccount.tenor > 1) {
          endDay = tenorConfigData.endDay
          deltaMonth = tenorConfigData.deltaMonth
        }

        let nextDayMonthyObj = common.getFirstNextTenor(nextDayMonthy, endDay, deltaMonth, loanAccount.tenor)
        nextDayMonthy = common.convertDatetoString(nextDayMonthyObj, 'yyyy-mm-dd')
      } else {
        if (tempStartMonthyDay > loanAccount.bill_day && i == 0 && j == 0) {
          const tempDate = new Date(nextDayMonthy)
          tempDate.setDate(loanAccount.bill_day)
          nextDayMonthy = common.calNextCycleDate2(1, common.convertDatetoString(tempDate, 'yyyy-mm-dd'))
        } else if (tempStartMonthyDay < loanAccount.bill_day && i == 0 && j == 0) {
          const tempDate = new Date(nextDayMonthy)
          tempDate.setDate(loanAccount.bill_day)
          nextDayMonthy = common.convertDatetoString(tempDate, 'yyyy-mm-dd')
        } else if (tempFirstDay > loanAccount.bill_day && i == totalCycle - 1 && j == totalCycleMonth - 1) {
          const tempDate = common.getDayOfMonth(nextDayMonthy, tempFirstDay)
          nextDayMonthy = tempDate
        } else if (tempFirstDay < loanAccount.bill_day && i == totalCycle - 1 && j == totalCycleMonth - 1) {
          const tempDate = new Date(nextDayMonthy)
          tempDate.setDate(tempFirstDay)
          nextDayMonthy = common.calNextCycleDate2(1, common.convertDatetoString(tempDate, 'yyyy-mm-dd'))
        } else {
          nextDayMonthy =  common.calNextCycleDate2(1, nextDayMonthy)
        }
      }

      // áp dụng nhảy ngày kỳ cuối với DNSE và FINV
      if ([constant.PARTNER_CODE.DNSE, constant.PARTNER_CODE.FINV].includes(loanAccount.partner_code) && j === totalCycleMonth - 1) {
        nextDayMonthy = loanAccount.end_date
      }

      irEndDate = nextDayMonthy
      irDueDate = common.convertDatetoString(common.addDays(irEndDate, loanAccount.grace_day_number), 'yyyy-mm-dd')
      // let irDiffDate = common.roundUp(common.dateDiff(new Date(irEndDate), new Date(irStartDate)).days(), 0)

      const irDiffDate = common.getDifferenceInDays(irEndDate, irStartDate)
      if (irChargePrinObj != undefined) {
        irAmount = common.roundUp(
          (irChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * irDiffDate,
          calcuCfg.scale
        )
      }
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        irAmount,
        irAmount,
        nextDay,
        2,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        sumRemainPrinAmt,
        undefined,
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        calcuCfg.defaultFee,
        calcuCfg.defaultFee,
        nextDay,
        5,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        undefined,
        'Phi thu ho tung ky',
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      irNumCycle++
    }
  }
  return arrResult
}

const calCycleInstallmentFactoring = function (loanAccount, irChargePrinObj, preferentialIrChargePrinObj, amortId) {
  const calcuCfg = CALCUCFG
  const payload = config
  const arrResult = []
  let amountMonthy = common.roundUp(loanAccount.apr_limit_amt, calcuCfg.scale)
  const firstDay = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let nextDay = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let nextDayMonthy = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let endDay = common.convertDatetoString(loanAccount.end_date, 'yyyy-mm-dd')
  let startDate, endDate, dueDate, irStartDate, irEndDate, irDueDate, tempStartDay, tempStartMonthyDay
  let sumRemainPrinAmt = 0
  let totalCycle = 1
  const tempFirstDay = firstDay.split('-')[2]

  let irNumCycle = 1
  let totalPriAmt = 0
  for (let i = 0; i < totalCycle; i++) {
    tempStartDay = nextDay.split('-')[2]
    startDate = nextDay
    endDate = endDay

    dueDate = common.convertDatetoString(common.addDays(endDate, loanAccount.grace_day_number), 'yyyy-mm-dd')

    if (i == totalCycle - 1) amountMonthy = loanAccount.apr_limit_amt - totalPriAmt
    sumRemainPrinAmt = common.roundUp(loanAccount.apr_limit_amt - amountMonthy * i, calcuCfg.scale)
    arrResult.push([
      loanAccount.loan_id,
      i + 1,
      amountMonthy,
      amountMonthy,
      nextDay,
      1,
      1,
      payload.ownerId,
      payload.isTesting,
      payload.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      startDate,
      nextDay,
      dueDate,
      i + 1,
      undefined,
      undefined,
      sumRemainPrinAmt,
      undefined,
      undefined,
      amortId,
      undefined
    ])
    totalPriAmt += amountMonthy

    // tinh lai hang thang
    let totalCycleMonth = 1

    // them ban ghi lai va phi khi khoi tao istallment
    let irAmount = 0
    for (let j = 0; j < totalCycleMonth; j++) {
      tempStartMonthyDay = nextDayMonthy.split('-')[2]
      irStartDate = nextDayMonthy
      irEndDate = endDay

      irDueDate = common.convertDatetoString(common.addDays(irEndDate, loanAccount.grace_day_number), 'yyyy-mm-dd')
      const irDiffDate = common.getDifferenceInDays(irEndDate, irStartDate)

      if (irChargePrinObj != undefined) {
        irAmount = common.roundUp(
            (irChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * irDiffDate,
            calcuCfg.scale
        )

        if (preferentialIrChargePrinObj != undefined) {
          let preferentialDate = common.convertDatetoString(loanAccount.preferential_end_date, 'yyyy-mm-dd')
          const preferentialDiffDate = common.getDifferenceInDays(preferentialDate, irStartDate)

          irAmount = common.roundUp(
              (irChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * (irDiffDate - preferentialDiffDate),
              calcuCfg.scale
          ) + common.roundUp(
              (preferentialIrChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * preferentialDiffDate,
              calcuCfg.scale
          )
        }
      }

      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        irAmount,
        irAmount,
        nextDay,
        2,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        sumRemainPrinAmt,
        undefined,
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        calcuCfg.defaultFee,
        calcuCfg.defaultFee,
        nextDay,
        5,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        undefined,
        'Phi thu ho tung ky',
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      irNumCycle++
    }
  }
  return arrResult
}
function calCycleInstallmentV2(loanAccount, irChargePrinObj, amortId) {
  const calcuCfg = CALCUCFG
  const firstDate = new Date(loanAccount.start_date)
  const endDate = new Date(loanAccount.end_date)
  let numCycle = 0
  let tempStartDate = firstDate
  !loanAccount.periodicity && (loanAccount.periodicity = 1)
  !loanAccount.bill_day && (loanAccount.bill_day = firstDate.getDate())
  const listPeriod = []
  while (tempStartDate < endDate) {
    numCycle += 1
    let billDate = new Date(tempStartDate)

    billDate.setDate(loanAccount.bill_day)
    billDate.setMonth(billDate.getMonth() + loanAccount.periodicity)
    if (billDate > endDate) {
      billDate = endDate
    }
    const tempDueDate = moment(billDate)
    tempDueDate.add(loanAccount.grace_day_number, 'day')
    const tempEndDate = moment(billDate).format('YYYY-MM-DD')

    listPeriod.push({
      start_date: moment(tempStartDate).format('YYYY-MM-DD'),
      end_date: tempEndDate,
      due_date: tempDueDate.format('YYYY-MM-DD'),
      num_cycle: numCycle
    })
    tempStartDate = billDate
  }
  const actualNumCycle = numCycle
  numCycle = Math.min(numCycle, loanAccount.tenor / loanAccount.periodicity)
  const amountMonthy = common.roundUp(loanAccount.apr_limit_amt / numCycle, calcuCfg.scale)
  const listInstallment = []
  let sumRemainPrinAmt = loanAccount.apr_limit_amt
  let totalPrinAmt = 0
  let irAmount = 0
  for (const period of listPeriod) {
    let printAmount = amountMonthy
    let paymentStatus = INSTALLMENT.PAYMENT_STATUS.ACTIVE
    if (period.num_cycle == 1 && actualNumCycle > numCycle) {
      printAmount = 0
      paymentStatus = INSTALLMENT.PAYMENT_STATUS.DONE
    }
    if (period.num_cycle == actualNumCycle) {
      printAmount = sumRemainPrinAmt
    }
    listInstallment.push([
      loanAccount.loan_id,
      period.num_cycle,
      printAmount,
      printAmount,
      period.end_date,
      1,
      paymentStatus,
      config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      period.start_date,
      period.end_date,
      period.due_date,
      period.num_cycle,
      undefined,
      undefined,
      sumRemainPrinAmt,
      undefined,
      undefined,
      amortId,
      undefined
    ])

    const irDiffDate = common.getDifferencesDays(period.end_date, period.start_date)
    if (irChargePrinObj != undefined) {
      irAmount = common.roundUp(
        (irChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * irDiffDate,
        calcuCfg.scale
      )
    }
    listInstallment.push([
      loanAccount.loan_id,
      period.num_cycle,
      irAmount,
      irAmount,
      period.end_date,
      2,
      1,
      config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      period.start_date,
      period.end_date,
      period.due_date,
      period.num_cycle,
      undefined,
      period.num_cycle,
      sumRemainPrinAmt,
      undefined,
      irChargePrinObj.ir_value,
      amortId,
      undefined
    ])
    listInstallment.push([
      loanAccount.loan_id,
      period.num_cycle,
      calcuCfg.defaultFee,
      calcuCfg.defaultFee,
      period.end_date,
      5,
      1,
      config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      period.start_date,
      period.end_date,
      period.due_date,
      period.num_cycle,
      undefined,
      undefined,
      undefined,
      'Phí thu hộ từng kì',
      undefined,
      amortId,
      undefined
    ])
    totalPrinAmt += printAmount
    sumRemainPrinAmt = loanAccount.apr_limit_amt - totalPrinAmt
  }
  return listInstallment
}
function calCycleInstallmentFactoringV2(loanAccount, irChargePrinObj, amortId, loanAccountOders) {
  if (!Array.isArray(loanAccountOders) || loanAccountOders.length === 0) return null;

  const endDate = moment(loanAccount.end_date).format('YYYY-MM-DD');

  const listInstallment = []
  for (const orderObj of loanAccountOders) {
    listInstallment.push([
      loanAccount.loan_id,
      orderObj.order_index,
      orderObj.prin_amt,
      orderObj.prin_amt,
      endDate,
      INSTALLMENT.TYPE.PRIN,
      INSTALLMENT.PAYMENT_STATUS.ACTIVE,
      config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      loanAccount.start_date,
      endDate,
      endDate,
      orderObj.order_index,
      undefined,
      undefined,
      null,
      undefined,
      undefined,
      amortId,
      undefined,
      moment(orderObj.payment_date).format('YYYY-MM-DD'),
      orderObj.order_number,
      moment(orderObj.payment_date).format('YYYY-MM-DD'),
    ])
  }

  return listInstallment
}
const calCycleInstallmentCreditLineV2 = function (loanAccount, irChargePrinObj, amortId) {
  const calcuCfg = CALCUCFG
  const payload = config
  const arrResult = []
  let amountMonthy = common.roundUp(
    (loanAccount.apr_limit_amt / loanAccount.tenor) * loanAccount.periodicity,
    calcuCfg.scale
  )
  const firstDay = common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd')
  let nextDay = firstDay
  let nextDayMonthy = firstDay
  let totalCycle = loanAccount.tenor / loanAccount.periodicity
  const tempFirstDay = firstDay.split('-')[2]

  if (
    loanAccount.partner_code !== PARTNER_CODE.VUIAPP && // chỉ tăng nếu không phải VUIAPP
    tempFirstDay != loanAccount.bill_day &&
    totalCycle > 1
  ) {
    totalCycle++
  }

  let irNumCycle = 1
  let totalPriAmt = 0
  let sumRemainPrinAmt = 0

  for (let i = 0; i < totalCycle; i++) {
    let startDate = nextDay
    // Tính toán ngày kết thúc kỳ
    // Nếu partner_code là VUIAPP và là kỳ đầu tiên, endDate là ngày loanAccount.bill_day tháng tiếp theo của startDate
    if (loanAccount.partner_code === PARTNER_CODE.VUIAPP && i === 0) {
      const startDateObj = new Date(startDate)
      // Lấy ngày loanAccount.bill_day của tháng tiếp theo
      const endDateObj = new Date(startDateObj.getFullYear(), startDateObj.getMonth() + 1, loanAccount.bill_day)
      nextDay = common.convertDatetoString(endDateObj, 'yyyy-mm-dd')
    } else {
      nextDay = common.calNextCycleDate2(loanAccount.periodicity, nextDay)
    }
    let endDate = nextDay
    let dueDate = common.convertDatetoString(common.addDays(endDate, loanAccount.grace_day_number), 'yyyy-mm-dd')

    if (i == totalCycle - 1) amountMonthy = loanAccount.apr_limit_amt - totalPriAmt
    sumRemainPrinAmt = common.roundUp(loanAccount.apr_limit_amt - amountMonthy * i, calcuCfg.scale)
    arrResult.push([
      loanAccount.loan_id,
      i + 1,
      amountMonthy,
      amountMonthy,
      nextDay,
      1,
      1,
      payload.ownerId,
      payload.isTesting,
      payload.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      startDate,
      nextDay,
      dueDate,
      i + 1,
      undefined,
      undefined,
      sumRemainPrinAmt,
      undefined,
      undefined,
      amortId,
      undefined
    ])
    totalPriAmt += amountMonthy

    // Tính lãi và phí từng kỳ
    let totalCycleMonth = loanAccount.periodicity
    let irAmount = 0
    for (let j = 0; j < totalCycleMonth; j++) {
      let irStartDate = nextDayMonthy
      nextDayMonthy = common.calNextCycleDate2(1, nextDayMonthy)
      let irEndDate = nextDayMonthy
      let irDueDate = common.convertDatetoString(common.addDays(irEndDate, loanAccount.grace_day_number), 'yyyy-mm-dd')
      // --- BẮT ĐẦU BỔ SUNG LOGIC VUIAPP ---
      let irDiffDate = common.getDifferenceInDays(irEndDate, irStartDate)
      let feeValue = calcuCfg.defaultFee
      // Nếu là VUIAPP thì dùng ngày của kỳ gốc
      if (loanAccount.partner_code === PARTNER_CODE.VUIAPP) {
        irStartDate = startDate
        irEndDate = endDate
        irDueDate = dueDate
        irDiffDate = common.getDifferenceInDays(endDate, startDate)
        feeValue = 0
      }
      // --- KẾT THÚC BỔ SUNG LOGIC VUIAPP ---
      if (irChargePrinObj != undefined) {
        irAmount = common.roundUp(
          (irChargePrinObj.ir_value / calcuCfg.totalDayOfYear) * sumRemainPrinAmt * irDiffDate,
          calcuCfg.scale
        )
      }
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        irAmount,
        irAmount,
        nextDay,
        2,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        sumRemainPrinAmt,
        undefined,
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      if (feeValue > 0) {
      arrResult.push([
        loanAccount.loan_id,
        i + 1,
        feeValue, // phí bằng 0 nếu là VUIAPP kỳ cuối
        feeValue,
        nextDay,
        5,
        1,
        payload.ownerId,
        payload.isTesting,
        payload.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        irStartDate,
        irEndDate,
        irDueDate,
        i + 1,
        undefined,
        irNumCycle,
        undefined,
        'Phi thu ho tung ky',
        irChargePrinObj.ir_value,
        amortId,
        undefined
      ])
      }
      // Tăng số kỳ lãi
      irNumCycle++
    }
  }
  return arrResult
}

function calCycleInstallmentCashLoan({ loanAccountObj, irChargePrinObj, promotion, feeObj, amortId, otherFees }) {
  const loanAccount = { ...loanAccountObj }
  const { data, emi } = getEmiSimulation({
    startDate: loanAccount.start_date,
    tenor: loanAccount.tenor,
    interestRate: Number(irChargePrinObj.ir_value),
    periodicity: Number(loanAccount.periodicity),
    graceDayNumber: Number(loanAccount.grace_day_number),
    aprLimitAmt: Number(loanAccount.apr_limit_amt),
    promotionAmount: promotion?.total_promotion_amount || 0
  })
  const listInstallment = []
  const recordsInstallmentDeduction = []
  const listPeriod = data.listPeriod
  let feePeriodials = []
  let feePickTimes = []
  if (otherFees && otherFees.length) {
    feePeriodials = otherFees.filter((item) => item.fee_type == FEE.FEE_CAL_TYPE.PERIODICAL)
    feePickTimes = otherFees.filter((item) => item.fee_type == FEE.FEE_CAL_TYPE.PICK_TIME)
  }

  for (const period of listPeriod) {
    if (feeObj) {
      listInstallment.push([
        loanAccount.loan_id,
        period.numCycle,
        feeObj.fee_amt ?? CALCUCFG.defaultFee,
        feeObj.fee_amt ?? CALCUCFG.defaultFee,
        period.endDate,
        INSTALLMENT.TYPE.FEE,
        PAYMENT_STATUS.INIT,
        config.ownerId,
        config.isTesting,
        config.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        period.startDate,
        period.endDate,
        period.dueDate,
        period.numCycle,
        undefined,
        undefined,
        period.sumRemainPrinAmt,
        'Phi tung ky',
        undefined,
        amortId,
        undefined
      ])
    }
    for (const feePeriodial of feePeriodials) {
      let feeTmp = 0
      if (feePeriodial.calcula_type == FEE.TYPE_CALCULATE.RECIPE) {
        feeTmp = common.roundUp(period.prinAmount * feePeriodial.fee_amt, global.calcuCfg.scale)
      } else {
        feeTmp = feePeriodial.fee_amt
      }
      listInstallment.push([
        loanAccount.loan_id,
        period.numCycle,
        feeTmp,
        feeTmp,
        period.endDate,
        INSTALLMENT.TYPE.FEE,
        PAYMENT_STATUS.INIT,
        loanAccount.owner_id || config.ownerId,
        config.isTesting,
        config.createdBy,
        loanAccount.contract_number,
        loanAccount.debt_ack_contract_number,
        period.startDate,
        period.endDate,
        period.dueDate,
        period.numCycle,
        undefined,
        undefined,
        period.sumRemainPrinAmt,
        feePeriodial.name,
        undefined,
        amortId,
        feePeriodial.priority
      ])
    }
    for (const feePick of feePickTimes) {
      if (feePick.instal_from <= period.numCycle && feePick.instal_to >= period.numCycle) {
        let feeTmp = 0
        if (feePick.calcula_type == FEE.TYPE_CALCULATE.RECIPE) {
          feeTmp = common.roundUp(period.prinAmount * feePick.fee_amt, global.calcuCfg.scale)
        } else {
          feeTmp = feePick.fee_amt
        }
        listInstallment.push([
          loanAccount.loan_id,
          period.numCycle,
          feeTmp,
          feeTmp,
          period.endDate,
          INSTALLMENT.TYPE.FEE,
          PAYMENT_STATUS.INIT,
          loanAccount.owner_id || config.ownerId,
          config.isTesting,
          config.createdBy,
          loanAccount.contract_number,
          loanAccount.debt_ack_contract_number,
          period.startDate,
          period.endDate,
          period.dueDate,
          period.numCycle,
          undefined,
          undefined,
          period.sumRemainPrinAmt,
          feePick.name,
          undefined,
          amortId,
          feePick.priority
        ])
      }
    }
    listInstallment.push([
      loanAccount.loan_id,
      period.numCycle,
      period.irAmount,
      period.irAmount,
      period.endDate,
      INSTALLMENT.TYPE.INT,
      PAYMENT_STATUS.INIT,
      loanAccount.owner_id || config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      period.startDate,
      period.endDate,
      period.dueDate,
      period.numCycle,
      undefined,
      period.numCycle,
      period.sumRemainPrinAmt,
      'Lãi từng kì',
      irChargePrinObj.ir_value,
      amortId,
      undefined
    ])
    listInstallment.push([
      loanAccount.loan_id,
      period.numCycle,
      period.prinAmount,
      period.prinAmount,
      period.endDate,
      INSTALLMENT.TYPE.PRIN,
      PAYMENT_STATUS.INIT,
      loanAccount.owner_id || config.ownerId,
      config.isTesting,
      config.createdBy,
      loanAccount.contract_number,
      loanAccount.debt_ack_contract_number,
      period.startDate,
      period.endDate,
      period.dueDate,
      period.numCycle,
      undefined,
      undefined,
      period.sumRemainPrinAmt,
      'Gốc từng kì',
      undefined,
      amortId,
      undefined
    ])
    if (period.deductionAmount) {
      recordsInstallmentDeduction.push({
        debt_ack_contract_number: loanAccount.debt_ack_contract_number,
        promotion_code: promotion.promotion_code,
        num_cycle: period.numCycle,
        deduction_amount: period.deductionAmount,
        original_amount: period.originalIrAmount,
        amort_id: amortId
      })
    }
  }
  return { listInstallment, recordsInstallmentDeduction, emi }
}

async function getInstallmentByContractLimit(pl) {
  console.log('req query getInstallmentByContractLimit: ', JSON.stringify(pl))
  try {
    if (pl.contractNumber == undefined) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Chưa truyen ma HD han muc'
      }
    }
    const listKunn = await LoanAccountRepository.findAllBy({ contract_number: pl.contractNumber })
    const result = await Promise.all(
      listKunn.map(async (kunn) => {
        const dataInsmKunn = await getInsmInfo({ debtAckContractNumber: kunn.debt_ack_contract_number })
        return {
          debtAckContractNumber: kunn.debt_ack_contract_number,
          financialDetail: dataInsmKunn.financialDetail,
          listInstallment: dataInsmKunn.data
        }
      })
    )
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Successs',
      data: result
    }
  } catch (error) {
    console.log('throw error: ', error)
    throw error
  }
}
module.exports = {
  getInstallmentSMA,
  getInsmInfo,
  getInstallmentInfoMisa,
  processListInstallment,
  exportDataInstallment,
  getNextInsmInfoByContractNumber,
  getHistoryPaidSme,
  simulationVoucherInstallment,
  getEmiSimulation,
  getInstallmentSimulationMerchant,
  calCycleInstallmentCashLoan,
  calCycleInstallmentCreditLine,
  calCycleInstallmentFactoring,
  calCycleInstallmentV2,
  getInstallmentByContractLimit,
  calCycleInstallmentCreditLineV2,
  calCycleInstallmentFactoringV2
}
