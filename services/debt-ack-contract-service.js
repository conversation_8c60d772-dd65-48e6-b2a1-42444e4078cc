const common = require('../utils/common')
const constant = require('../utils/constant')
const camelcaseKeys = require('camelcase-keys')
const debtAckContractRepo = require('../repositories/debt-ack-contract-repo')
const mcLimitRepo = require('../repositories/merchant-limit-repo')
const installmentRepo = require('../repositories/installment-repo')
const disbursementRepo = require('../repositories/disbursement-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const installmentDeductionRepo = require('../repositories/promotion-installment-deduction-repo')
const promotionRepo = require('../repositories/promotion-repo')
const voucherRepo = require('../repositories/voucher-repo')
const loanEmiHistoryRepo = require('../repositories/loan-emi-history-repo')
const crmService = require('../other-services/crm-service')
const tranLogRepo = require('../repositories/tran-log-repo')
const contractRiskGrpService = require('../services/contract-risk-grp-service')
const installmentService = require('../services/installment-service')
const losService = require('../other-services/los-service')
const disbursementService = require('../other-services/disbursement-service')
const repaymentService = require('../services/repayment-service')
const feesRepo = require('../repositories/fees-repo')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const moment = require('moment')
const lodash = require('lodash')
const loanAccountRepo = require('../repositories/loan-account-repo')
const holidayService = require('../services/holiday-service')
const loanStatusHstService = require('../services/loan-status-hst-service')
const welcomePackageService = require('../services/welcome-package-service')
const actionAuditService = require('../other-services/action-audit-service')
const feeDetailRepo = require('./../repositories/fee-detail-repo')
const insuranceService = require('./insurance-service')
const { isCashLoanPartnerCode, isLosUnitedPartnerCode} = require('../utils/helper')
const { LoanAccountRepository, InsuranceRepository, DisbursementRepository } = require('../repositories-v2')
const productService = require('../other-services/product-service')
const loanAccountV2Repo = require("../repositories/loan-account-repo");
const {DEBT_ACK_STATUS} = require("../utils/constant");
const contractRiskGrpRepo = require("../repositories/contract-risk-grp-repo");
const loanRgRepo = require("../repositories/loan-risk-grp-repo");
const {checkCacheFieldRefund, clearCacheFieldRefund, setCacheFieldRefund} = require("../utils/redis");
const {sleep} = require("../utils/common");
const helper = require("../utils/helper");
const smsService = require("../other-services/sms-service");

const DEFAULT_TIME_WAIT = 3 * 1000

/**
 * Tao khe uoc nhan no
 * @param {*} req
 * @param {*} res
 */
const createDebtAckContract = async function (req) {
  try {
    const payload = req.body
    console.log('req body createDebtAckContract: ', JSON.stringify(payload))

    const validated = validateInputCreateKunn(payload)

    if (!validated.isSuccess) {
      return {
        code: 1,
        message: validated.message,
        statusCode: 400
      }
    }
    const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

    if (findLoanAcc.length) {
      return {
        code: 1,
        message: '[MC-LMS] Ma KUNN da ton tai',
        statusCode: 200
      }
    }
    payload.holdMoney = 0
    payload.status = 0
    payload.ownerId = payload.ownerId || constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting
    !payload.disbursementType && (payload.disbursementType = constant.DISBURSEMENT.TYPE.TRANSFER)
    const plLoanAcount = {
      contractNumber: payload.contractNumber,
      paymentStatus: 1
    }

    const listFunc = await Promise.all([
      mcLimitRepo.findActiveByContractNumberAndCustId(global.poolRead, payload),
      debtAckContractRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, plLoanAcount),
      loanAccountRepo.findFirstActiveKunnByContractNumberAndContractType(payload.contractNumber),
      productService.getProductVoucherV2(payload.productCode, payload.amount)
    ])
    const rsMclimit = listFunc[0]
    const rsLoanAcc = listFunc[1]

    if (rsMclimit.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] han muc chua duoc khoi tao',
        statusCode: 200
      }
    }
    if (!isCashLoanPartnerCode(payload.partnerCode)) {
      let remainLimitAmount = Number(rsMclimit.rows[0].apr_limit_amt)
      for (const loanAccObj of rsLoanAcc.rows) {
        remainLimitAmount -= Number(loanAccObj.prin_amt)
      }
      if (remainLimitAmount < payload.amount) {
        return {
          code: 1,
          message: '[MC-LMS] han muc con lai cua khe uoc khong du',
          data: {
            remainLimitAmount
          },
          statusCode: 200
        }
      }
    }
    const loanContractLimit = rsMclimit.rows[0]
    payload.custId = loanContractLimit.cust_id
    payload.contractLimitId = loanContractLimit.contract_limit_id
    payload.ccycd = payload.ccycd || 'VND'
    payload.disbursementAmount = payload.amount

    payload.graceDayNumber = payload.graceDayNumber || 0
    !payload.phoneNumber && (payload.phoneNumber = loanContractLimit.phone_number)

    const firstActiveKunn = listFunc[2]
    const now = new Date()
    if (firstActiveKunn && payload.partnerCode == constant.PARTNER_CODE.MISA) {
      payload.billDay = firstActiveKunn.bill_day
      if (payload.contractType == constant.CONTRACT_TYPE.CASHLOAN) {
        payload.tenor = firstActiveKunn.tenor
      }
      const differentDays = common.getDifferencesDays(firstActiveKunn.active_date, now)
      if (
        payload.contractType == constant.CONTRACT_TYPE.CASHLOAN &&
        differentDays > constant.MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN
      ) {
        return {
          code: 1,
          message: '[MC-LMS] Thoi gian tao KUNN lon hon 30 ngay so voi kunn 1',
          statusCode: 400
        }
      }
    }
    const rsIns = await debtAckContractRepo.insDebtAckContract(global.poolWrite, payload)
    const loanAccObj = rsIns.rows[0]
    loanStatusHstService.insertNewAndUpdateOldRecord(
      loanAccObj.debt_ack_contract_number,
      constant.LOAN_ACC_STATUS.SIG,
      now
    )

    insertIrCharge(payload)

    if (isCashLoanPartnerCode(payload.partnerCode)) {
      if (!lodash.isEmpty(payload.voucher)) {
        voucherRepo.insertVoucher({
          ...payload.voucher,
          debtAckContractNumber: payload.debtAckContractNumber
        })
      }
      if (!lodash.isEmpty(payload.promotion)) {
        insertPromotion(payload, loanAccObj)
      }
    }
    const arrFee = payload.fee
    const resProduct = listFunc[3]
    payload.product = resProduct
    await insertFees(arrFee, payload)
    await insuranceService.insertInsurances(payload, loanAccObj)

    disbursementService.sendDisbursementRequest(payload, loanAccObj)

    return {
      code: 0,
      message: '[MC-LMS] Tao khe uoc nhan no thanh cong',
      data: camelcaseKeys(loanAccObj),
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}
/**
 * Active khe uoc nhan no
 * @param {*} req
 * @param {*} res
 */
const activeDebtAckContract = async function (req) {
  try {
    const payload = req.body
    console.log('req body activeDebtAckContract: ', JSON.stringify(payload))
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])

    if (rsCheck.rowCount == 0 && rsCheckByLoanId.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN khong ton tai',
        statusCode: 200
      }
    }
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    if (loanInfo.status != constant.DEBT_ACK_STATUS.SIG) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN da duoc kich hoat',
        statusCode: 200
      }
    }
    const fromDate = payload.fromDate || moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    const toDate = await getEndDateOfKunn(loanInfo, fromDate)
    if (!toDate) {
      return {
        code: 1,
        message: '[MC-LMS] Kich hoat KUNN that bai. Thoi gian kich hoat KUNN lon hon 30 ngay so voi kunn 1',
        statusCode: 400
      }
    }
    const { loanAccountObj, rsInsmIn, irChargePrinObj } = await handleGenerateInstallment(fromDate, toDate, loanInfo)

    loanStatusHstService.insertNewAndUpdateOldRecord(loanInfo.debt_ack_contract_number, constant.LOAN_ACC_STATUS.ACT)

    const mcLimitPayload = {
      mcLimitId: loanAccountObj.contract_limit_id,
      amount: loanAccountObj.apr_limit_amt
    }
    mcLimitRepo.updateRemainAmount(global.poolWrite, mcLimitPayload)
    const disburseObj = await insertTransLogAndUpdateDisbursementStatus(payload, loanAccountObj)
    crmService.callCrmCreateKunn(loanAccountObj)
    if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
      crmService.activeLoan(loanAccountObj.debt_ack_contract_number)
    }
    losService.callActiveKunn(loanAccountObj)

    insuranceService.activeInsurances(loanAccountObj)
    // cap nhat khoi tao dpd risk group
    contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj: loanAccountObj,
      payload: { debtAckContractNumber: loanAccountObj.debt_ack_contract_number, calDate: fromDate }
    })

    welcomePackageService.sendEmailWelcome(loanAccountObj, disburseObj, rsInsmIn, irChargePrinObj)
    actionAuditService.saveActionAudit(
      loanAccountObj.debt_ack_contract_number,
      { actionAuditType: 'CONTRACT_GENERATION', actionCodeType: 'WAITING_TO_BE_GENERATED' },
      { createdUser: payload.createdBy }
    )
    if (helper.isSendSmsPartnerCode(loanAccountObj.partner_code)) {
      let smsContent = global.config?.data?.sms?.smsDisburseSuccess?.replace('${contractNumber}', loanAccountObj.debt_ack_contract_number)

      smsService.sendSMS({
        phoneNumber: loanAccountObj.phone_number,
        content: smsContent,
      })
    }
    return {
      code: 0,
      message: '[MC-LMS] Kich hoat KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}


const refundSuspendFactoringDebtAckContract = async function (req) {
  const payload = req.body
  console.log('req body refundSuspendFactoringDebtAckContract: ', JSON.stringify(payload))

  try {
    const checkCache = await checkCacheFieldRefund(payload.debtAckContractNumber)
    if (checkCache) {
      await sleep(DEFAULT_TIME_WAIT)
      return refundSuspendFactoringDebtAckContract(req)
    }
    setCacheFieldRefund(payload.debtAckContractNumber)

    const loanAccObj = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } });

    if (!loanAccObj || loanAccObj.status !== constant.LOAN_ACC_STATUS.TER) {
      await clearCacheFieldRefund(payload.debtAckContractNumber)
      return {
        code: 1,
        message: '[MC-LMS] Mã hd không hợp lệ',
        statusCode: 200
      }
    }

    if ((Number(loanAccObj.suspend_amt || 0) + Number(loanAccObj.non_allocation_amt || 0)) !== Number(payload.amt)) {
      await clearCacheFieldRefund(payload.debtAckContractNumber)
      return {
        code: 1,
        message: '[MC-LMS] Số tiền hoàn không hợp lệ',
        statusCode: 200
      }
    }

    payload.partnerCode = loanAccObj.partner_code
    payload.contractNumber = loanAccObj.contract_number

    await repaymentService.refundProcess(
        Number(loanAccObj.suspend_amt || 0),
        Number(loanAccObj.non_allocation_amt || 0),
        payload.debtAckContractNumber
    )
    const dateNow = new Date()
    const plInsertTransLog = {
      contractNumber: loanAccObj.contract_number,
      loanId: loanAccObj.loan_id,
      tranType: 'BANK',
      refId: payload.partnerTranNo,
      amtNumber: Number(payload.amt),
      tranDate: dateNow,
      valueDate: dateNow,
      tranDesc: 'Refund factoring for ' + loanAccObj.debt_ack_contract_number,
      tranStatus: '200',
      createdUser: constant.config.createdBy,
      sessionId: dateNow.getTime(),
      ownerId: constant.config.ownerId
    }
    await tranLogRepo.insTranLog(global.poolWrite, plInsertTransLog)
    await disbursementService.refundFactoring(payload)

    await clearCacheFieldRefund(payload.debtAckContractNumber)

    return {
      code: 0,
      message: '[MC-LMS] Refund suspend factoring KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    await clearCacheFieldRefund(payload.debtAckContractNumber)

    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

const callbackRefundSuspendDebtAckContract = async function (req) {
  try {
    const payload = req.body
    console.log('req body callbackRefundSuspendDebtAckContract: ', JSON.stringify(payload))

    const loanAccObj = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.contractNumber } });

    if (!loanAccObj || loanAccObj.status !== constant.LOAN_ACC_STATUS.TER) {
      return {
        code: 1,
        message: '[MC-LMS] Mã hd không hợp lệ',
        statusCode: 200
      }
    }

    losService.callRefundRequestStatus({
      contract_number: payload.contractNumber,
      is_success: payload.status == 1,
    })

    return {
      code: 0,
      message: '[MC-LMS] Callback Refund suspend KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

const transferSuspendFactoringDebtAckContract = async function (req) {
  try {
    const payload = req.body
    console.log('req body transferSuspendFactoringDebtAckContract: ', JSON.stringify(payload))

    const loanAccObj = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } });

    if (!loanAccObj || !loanAccObj.status !== constant.LOAN_ACC_STATUS.TER) {
      return {
        code: 1,
        message: '[MC-LMS] Mã hd không hợp lệ',
        statusCode: 200
      }
    }

    const toLoanAccObj = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.toDebtAckContractNumber } });

    if (!toLoanAccObj || !loanAccObj.status !== constant.LOAN_ACC_STATUS.ACT) {
      return {
        code: 1,
        message: '[MC-LMS] Mã hd không hợp lệ',
        statusCode: 200
      }
    }

    if ((Number(loanAccObj.suspend_amt || 0) + Number(loanAccObj.non_allocation_amt || 0)) !== Number(payload.amt)) {
      return {
        code: 1,
        message: '[MC-LMS] Số tiền hoàn không hợp lệ',
        statusCode: 200
      }
    }

    await loanAccountV2Repo.updateLoanAccountSuspendAmtAndNonAllocationAmt(global.poolWrite, {
      debtAckContractNumber: payload.debtAckContractNumber,
      toCollect: Number(payload.amt),
      suspendAmt: -Number(loanAccObj.suspend_amt || 0),
      nonAllocateAmt: -Number(loanAccObj.non_allocation_amt || 0)
    })
    await loanAccountV2Repo.updateLoanAccountSuspendAmtAndNonAllocationAmt(global.poolWrite, {
      debtAckContractNumber: payload.toDebtAckContractNumber,
      toCollect: -Number(payload.amt),
      nonAllocateAmt: Number(payload.amt)
    })

    await repaymentService.doRepayment({
      debtAckContractNumber: payload.toDebtAckContractNumber,
      paymentDate: moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    })

    return {
      code: 0,
      message: '[MC-LMS] Transfer suspend factoring KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

/**
 *
 * @param {*} req
 * @returns
 */
const activeDebtAckContractV2 = async function (req) {
  try {
    const payload = req.body
    console.log('req body activeDebtAckContract v2: ', JSON.stringify(payload))
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])

    if (rsCheck.rowCount == 0 && rsCheckByLoanId.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN khong ton tai',
        statusCode: 200
      }
    }
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    if (loanInfo.status != constant.DEBT_ACK_STATUS.SIG) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN da duoc kich hoat',
        statusCode: 200
      }
    }
    const fromDate = payload.fromDate || moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    const toDate = await getEndDateOfKunn(loanInfo, fromDate)
    if (!toDate) {
      return {
        code: 1,
        message: '[MC-LMS] Kich hoat KUNN that bai. Thoi gian kich hoat KUNN lon hon 30 ngay so voi kunn 1',
        statusCode: 400
      }
    }
    const { loanAccountObj, rsInsmIn, irChargePrinObj } = await handleGenerateInstallment(fromDate, toDate, loanInfo)

    loanStatusHstService.insertNewAndUpdateOldRecord(loanInfo.debt_ack_contract_number, constant.LOAN_ACC_STATUS.ACT)

    const mcLimitPayload = {
      mcLimitId: loanAccountObj.contract_limit_id,
      amount: loanAccountObj.apr_limit_amt
    }
    mcLimitRepo.updateRemainAmount(global.poolWrite, mcLimitPayload)
    // const disburseObj = await insertTransLogAndUpdateDisbursementStatus(payload, loanAccountObj)
    crmService.callCrmCreateKunn(loanAccountObj)
    if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
      crmService.activeLoan(loanAccountObj.debt_ack_contract_number)
    }
    losService.callActiveKunn(loanAccountObj)

    insuranceService.activeInsurances(loanAccountObj)
    // cap nhat khoi tao dpd risk group
    contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj: loanAccountObj,
      payload: { debtAckContractNumber: loanAccountObj.debt_ack_contract_number, calDate: fromDate }
    })
    const disburseObj = {
      amt: 0,
      email: ''
    }
    const disburseObjs = await disbursementRepo.findByDebtAckContractNumber(loanAccountObj.debt_ack_contract_number);
    if(disburseObjs?.[0]?.email)
    {
      disburseObj.email = disburseObjs?.[0]?.email;
    }
    welcomePackageService.sendEmailWelcome(loanAccountObj, disburseObj, rsInsmIn, irChargePrinObj)
    actionAuditService.saveActionAudit(
      loanAccountObj.debt_ack_contract_number,
      { actionAuditType: 'CONTRACT_GENERATION', actionCodeType: 'WAITING_TO_BE_GENERATED' },
      { createdUser: payload.createdBy }
    )
    return {
      code: 0,
      message: '[MC-LMS] Kich hoat KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

const activeBackdateDebtAckContract = async function (req) {
  try {
    const payload = req.body
    console.log('req body activeBackdateDebtAckContract v2: ', JSON.stringify(payload))
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])

    if (rsCheck.rowCount == 0 && rsCheckByLoanId.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN khong ton tai',
        statusCode: 200
      }
    }
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    if (loanInfo.status != constant.DEBT_ACK_STATUS.ACTIVE) {
      return {
        code: 1,
        message: '[MC-LMS] KUNN chua duoc kich hoat',
        statusCode: 200
      }
    }
    const fromDate = payload.fromDate || moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    const toDate = await getEndDateOfKunn(loanInfo, fromDate)
    if (!toDate) {
      return {
        code: 1,
        message: '[MC-LMS] Kich hoat KUNN that bai. Thoi gian kich hoat KUNN lon hon 30 ngay so voi kunn 1',
        statusCode: 400
      }
    }

    // rollback
    await loanAmortRepo.deleteByDebtAckContractNumber(loanInfo.debt_ack_contract_number)
    await installmentRepo.deleteByDebtAckContractNumber(loanInfo.debt_ack_contract_number)
    await contractRiskGrpRepo.deleteByDebtAckContractNumber(loanInfo.debt_ack_contract_number)
    await loanRgRepo.deleteByDebtAckContractNumber(loanInfo.debt_ack_contract_number)
    await loanAccountRepo.updateStatusLoanAccount(loanInfo.debt_ack_contract_number, DEBT_ACK_STATUS.SIG)

    const { loanAccountObj, rsInsmIn, irChargePrinObj } = await handleGenerateInstallment(fromDate, toDate, loanInfo)

    loanStatusHstService.insertNewAndUpdateOldRecord(loanInfo.debt_ack_contract_number, constant.LOAN_ACC_STATUS.ACT)

    contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj: loanAccountObj,
      payload: { debtAckContractNumber: loanAccountObj.debt_ack_contract_number, calDate: fromDate }
    })
    const disburseObj = {
      amt: 0,
      email: ''
    }
    const disburseObjs = await disbursementRepo.findByDebtAckContractNumber(loanAccountObj.debt_ack_contract_number);
    if(disburseObjs?.[0]?.email)
    {
      disburseObj.email = disburseObjs?.[0]?.email;
    }
    welcomePackageService.sendEmailWelcome(loanAccountObj, disburseObj, rsInsmIn, irChargePrinObj)
    actionAuditService.saveActionAudit(
      loanAccountObj.debt_ack_contract_number,
      { actionAuditType: 'CONTRACT_GENERATION', actionCodeType: 'WAITING_TO_BE_GENERATED' },
      { createdUser: payload.createdBy }
    )
    return {
      code: 0,
      message: '[MC-LMS] Kich hoat KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

const updateDisburserment = async function(req){
  try{
    const payload = req.body
    console.log('req body updateDisburserment v2: ', JSON.stringify(payload))
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    await insertTransLogAndUpdateDisbursementStatusV2(payload, loanInfo)

    return {
      code: 0,
      message: '[MC-LMS] Update disbursement status success',
      statusCode: 200
    }

  } catch (error) {
    console.log(error)
    console.error('Error while updateDisburserment:', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}
const getListLoanActive = async function (req, res) {
  try {
    const pl = {
      status: 1
    }
    const rsLoanAcc = await debtAckContractRepo.findLoanAccByPaymentStatusNotCompleted(global.poolRead, pl)
    if (rsLoanAcc.rowCount > 0) {
      const listDebt = []
      for (const i in rsLoanAcc.rows) {
        listDebt.push(rsLoanAcc.rows[i].debt_ack_contract_number)
      }
      return res.status(200).json(
        (res.body = {
          code: 0,
          message: '[LMS-MC] Thuc hien lay danh sach Loan thanh cong',
          data: listDebt
        })
      )
    }
    return res.status(200).json(
      (res.body = {
        code: 1,
        message: '[LMS-MC] Khong tim thay danh sach Loan'
      })
    )
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}

async function insTranLog(loanAccObj, payload) {
  const dateNow = moment().toDate()
  const pl = {
    contractNumber: loanAccObj.contract_number,
    loanId: loanAccObj.loan_id,
    tranType: 'BANK',
    refId: payload.transactionId,
    amtNumber: loanAccObj.apr_limit_amt,
    tranDate: dateNow,
    valueDate: dateNow,
    tranDesc: 'Disbursement for ' + loanAccObj.debt_ack_contract_number,
    tranStatus: payload.respCode,
    createdUser: constant.config.createdBy,
    sessionId: dateNow.getTime(),
    ownerId: constant.config.ownerId
  }
  const rs = await tranLogRepo.insTranLog(global.poolWrite, pl)
  if (rs.rowCount > 0) {
    return rs.rows[0]
  }
}
function validateInputCreateKunn(payload) {
  if (
    !payload.debtAckContractNumber ||
    !payload.contractNumber ||
    !payload.amount ||
    !payload.contractType ||
    !payload.irCharge ||
    !payload.partnerCode ||
    !payload.tenor
  ) {
    return {
      message:
        'Thiếu một trong các trường debtAckContractNumber, contractNumber , amount, contractType, irCharge, partnerCode, tenor',
      isSuccess: false
    }
  }
  if (payload.irCharge.length < 3) {
    return {
      message: 'irCharge thiếu dữ liệu truyền vào',
      isSuccess: false
    }
  }
  if (
    payload.contractType == constant.CONTRACT_TYPE.CASHLOAN &&
    (payload.partnerCode == constant.PARTNER_CODE.MISA ||
      global.config?.data?.mobile?.listChannel.split(',').includes(payload.channel))
  ) {
    if (payload.irCharge.length < 7) {
      return {
        message: 'irCharge vay món thiếu dữ liệu truyền vào',
        isSuccess: false
      }
    }
    if (payload.irCharge.filter((item) => item.irName == constant.EARLY_TERMINATION_RATE_TYPE).length < 4) {
      return {
        message: 'irCharge vay món thiếu dữ liệu phí phạt truyền vào',
        isSuccess: false
      }
    }
    const findIrCharge = payload.irCharge.find(
      (item) =>
        item.irName == constant.EARLY_TERMINATION_RATE_TYPE &&
        item.tenorFrom <= payload.tenor &&
        item.tenorTo >= payload.tenor
    )
    if (!findIrCharge) {
      return {
        message: 'Dữ liệu phí phạt truyền vào không khớp với tenor',
        isSuccess: false
      }
    }
  }

  if (common.isFactoringLoanChannel(payload.channel)) {
    if (
        !payload.preferentialEndDate ||
        !payload.endDate ||
        !payload.agreementNumber ||
        !payload.agreementAmt
    ) {
      return {
        message:
            'Thiếu một trong các trường preferentialEndDate, endDate , agreementNumber, agreementAmt',
        isSuccess: false
      }
    }
  }

  const checkIrValueIsNumber = payload.irCharge.every((item) => !isNaN(item.irValue))

  return {
    isSuccess: checkIrValueIsNumber,
    message: 'irValue không hợp lệ'
  }
}

async function getEndDateOfKunn(loanInfo, fromDate) {
  if (common.isFactoringLoanChannel(loanInfo.channel)) {
    return new Date(loanInfo.end_date)
  }

  if (loanInfo.partner_code === constant.PARTNER_CODE.VUIAPP && loanInfo.contract_type === constant.CONTRACT_TYPE.CREDITLINE) {
    const startDateObj = new Date(fromDate)
    return new Date(startDateObj.getFullYear(), startDateObj.getMonth() + 1, loanInfo.bill_day)
  }

  let toDate = common.calNextCycleDateV3(loanInfo.tenor, fromDate)
  if (
    loanInfo.contract_type == constant.CONTRACT_TYPE.CASHLOAN &&
    loanInfo.partner_code == constant.PARTNER_CODE.MISA
  ) {
    const firstActiveKunn = await loanAccountRepo.findFirstActiveKunnByContractNumberAndContractType(
      loanInfo.contract_number
    )
    if (
      firstActiveKunn &&
      common.getDifferencesDays(firstActiveKunn?.active_date, fromDate) >
        constant.MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN
    ) {
      return false
    }
    toDate = firstActiveKunn ? firstActiveKunn.end_date : toDate
  }
  if (isLosUnitedPartnerCode(loanInfo.partner_code)) {
    const startDate = new Date(fromDate)
    const startDay = startDate.getDate()
    toDate = new Date(startDate)
    const tenorConfigData = global.tenorConfig[startDay]
    let endDay = startDay
    let deltaMonth = 1
    // if (tenorConfigData && loanInfo.tenor > 1) {
    //   endDay = tenorConfigData.endDay
    //   deltaMonth = tenorConfigData.deltaMonth
    // }
    if (tenorConfigData) {
      endDay = tenorConfigData.endDay
      deltaMonth = tenorConfigData.deltaMonth
    }
    toDate = common.getFirstNextTenor(toDate, endDay, deltaMonth, loanInfo.tenor)
    if (loanInfo.tenor > 1) {
      toDate = common.calNextCycleDateV3(loanInfo.tenor - 1, toDate)
    }
    toDate = holidayService.getNextDayAfterHoliday(toDate)
  }

  // FIND áp dụng nhảy ngày lễ nhưng không áp dụng tenor config
  if ([constant.PARTNER_CODE.FINV].includes(loanInfo.partner_code)) {
    const startDate = new Date(fromDate)
    const startDay = startDate.getDate()
    toDate = new Date(startDate)

    let endDay = startDay
    let deltaMonth = 1

    toDate = common.getFirstNextTenor(toDate, endDay, deltaMonth, loanInfo.tenor)
    if (loanInfo.tenor > 1) {
      toDate = common.calNextCycleDateV3(loanInfo.tenor - 1, toDate)
    }
    toDate = holidayService.getNextDayAfterHoliday(toDate)
  }

  return toDate
}
async function handleGenerateInstallment(fromDate, toDate, loanInfo) {
  const loanAccountObj = {
    ...loanInfo,
    status: constant.DEBT_ACK_STATUS.ACTIVE,
    start_date: fromDate,
    active_date: fromDate,
    end_date: toDate
  }
  // lay danh sach lai
  const plIrCharge = {
    debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
    productCode: loanAccountObj.product_code
  }
  const [rsIrCharge, promotion, rsFees, otherFees] = await Promise.all([
    irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, plIrCharge),
    promotionRepo.findPromotionByDebtAckContractNumber(loanAccountObj.debt_ack_contract_number),
    feesRepo.findByDebtAckAndCode({
      debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
      feeCode: constant.FEE_CODE.MONTHY
    }),
    feesRepo.findByDebtAckAndProductCode({
      debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
      productCode: loanAccountObj.product_code
    })
  ])
  const irChargePrinObj = rsIrCharge.rows.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN)
  const preferentialIrChargePrinObj = rsIrCharge.rows.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.PREFERENTIAL_RATE)
  const feeObj = rsFees.rows.find((item) => item.code == constant.FEE_CODE.MONTHY)

  const payloadInsertAmort = {
    debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
    amtAmort: Number(loanAccountObj.apr_limit_amt),
    tenor: loanAccountObj.tenor,
    startDate: fromDate,
    endDate: toDate,
    intRate: Number(irChargePrinObj.ir_value)
  }
  const amortId = await loanAmortRepo.insertLoanAmort(payloadInsertAmort)
  // tao danh sach ban ghi theo tung ky
  let listInstallment
  let recordsInstallmentDeduction = []
  if (
    isCashLoanPartnerCode(loanAccountObj.partner_code) ||
    (global.config?.data?.mobile?.listChannel.split(',').includes(loanAccountObj.channel) &&
      loanAccountObj.contract_type == constant.CONTRACT_TYPE.CASHLOAN)
  ) {
    const dataInstallment = installmentService.calCycleInstallmentCashLoan({
      loanAccountObj,
      irChargePrinObj,
      promotion,
      feeObj,
      amortId,
      otherFees: otherFees.rows
    })
    listInstallment = dataInstallment.listInstallment
    recordsInstallmentDeduction = dataInstallment.recordsInstallmentDeduction
    const { emi } = dataInstallment
    loanEmiHistoryRepo.insertEmiHistory(loanAccountObj, emi, constant.EVENT_DI_NAME.LOAN_ACTIVED)
  } else if (common.isFactoringLoanChannel(loanAccountObj.channel)) {
    listInstallment = installmentService.calCycleInstallmentFactoring(loanAccountObj, irChargePrinObj, preferentialIrChargePrinObj, amortId)
  } else if (loanAccountObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE && loanAccountObj.partner_code == constant.PARTNER_CODE.VUIAPP) {
    listInstallment = installmentService.calCycleInstallmentCreditLineV2(loanAccountObj, irChargePrinObj, amortId)
  } else if (loanAccountObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE) {
    listInstallment = installmentService.calCycleInstallmentCreditLine(loanAccountObj, irChargePrinObj, amortId)
  } else {
    listInstallment = installmentService.calCycleInstallmentV2(loanAccountObj, irChargePrinObj, amortId)
  }
  const rsInsmIn = await installmentRepo.insBatchInstallment(listInstallment)
  let totalIr = 0
  let totalFee = 0
  for (const insmObj of rsInsmIn.rows) {
    if (insmObj.type == constant.INSTALLMENT.TYPE.INT) {
      totalIr += Number(insmObj.amount)
    } else if (insmObj.type == constant.INSTALLMENT.TYPE.FEE) {
      totalFee += Number(insmObj.amount)
    }
    if (recordsInstallmentDeduction.length && insmObj.type == constant.INSTALLMENT.TYPE.INT) {
      const checkPromotionCycle = recordsInstallmentDeduction.find((item) => item.num_cycle == insmObj.num_cycle)
      checkPromotionCycle && (checkPromotionCycle.installment_id = insmObj.id)
    }
  }
  if(recordsInstallmentDeduction.length > 0) {
  installmentDeductionRepo.insertManyDeductionInstallment(recordsInstallmentDeduction)
  }
  // update bill loan account
  const minTenor = rsInsmIn.rows.find((item) => item.num_cycle == 1 && item.ir_num_cycle == 1)

  const payloadActiveKunn = {
    loanId: loanAccountObj.loan_id,
    startDate: fromDate,
    endDate: loanAccountObj.end_date,
    intAmt: totalIr,
    feeAmt: totalFee,
    rlsAmt: loanInfo.apr_limit_amt,
    billDay: minTenor?.end_date ? moment(minTenor.end_date).date() : loanAccountObj.bill_day
  }
  loanAccountRepo.updateActiveKunn(payloadActiveKunn)
  return { loanAccountObj: { ...loanAccountObj, bill_day: payloadActiveKunn.billDay }, rsInsmIn, irChargePrinObj }
}
async function insertFeesSMA(arrFee, payload) {
  if (Array.isArray(arrFee) && arrFee.length) {
    for (const arrFeeObj of arrFee) {
      const plFee = {
        productCode: payload.productCode,
        code: arrFeeObj.feeCode,
        name: arrFeeObj.feeName,
        feeAmt: Number(arrFeeObj.feeAmt || 0),
        ownerId: payload.ownerId,
        isTesting: payload.isTesting,
        createdBy: payload.createdBy,
        debtAckContractNumber: payload.debtAckContractNumber
      }
      feesRepo.insertFees(global.poolWrite, plFee)
    }
  }
}
async function insertFees(arrFee, payload) {
  const product = payload.product
  const arrFees = []
  if (Array.isArray(product?.fee) && product?.fee.length) {
    for (const arrFee of product?.fee) {
      const plFee = {
        productCode: payload.productCode,
        code: arrFee.prdctFeId.toString(),
        name: arrFee.feeName,
        feeAmt: Number(arrFee.value),
        ownerId: payload.ownerId,
        isTesting: payload.isTesting,
        createdBy: payload.createdBy,
        debtAckContractNumber: payload.debtAckContractNumber,
        feeType: arrFee.feeCalType,
        calculaType: arrFee.calculaType,
        instalFrom: arrFee.installmentFrom,
        instalTo: arrFee.installmentTo,
        priority: arrFee.priority
      }
      const feeInsertRs = await feesRepo.insertFees(global.poolWrite, plFee)
      arrFees.push(...feeInsertRs.rows)
    }
  } else if (Array.isArray(arrFee) && arrFee.length && Array.isArray(arrFee[0].feeDetail)) {
    const arrFeeDetail = arrFee[0].feeDetail
    for (const arrFeeObj of arrFeeDetail) {
      const plFee = {
        productCode: payload.productCode,
        code: arrFeeObj.feId.toString(),
        name: arrFeeObj.feeName,
        feeAmt: Number(arrFeeObj.isOnePayment),
        ownerId: payload.ownerId,
        isTesting: payload.isTesting,
        createdBy: payload.createdBy,
        debtAckContractNumber: payload.debtAckContractNumber
      }
      const feeInsertRs = await feesRepo.insertFees(global.poolWrite, plFee)
      arrFees.push(...feeInsertRs.rows)
    }
  }

  const feeFirtTimes = arrFees.filter((i) => i.fee_type == constant.FEE.FEE_CAL_TYPE.FIRST_TIME)
  let feeFistTimeAmt = 0
  for (const feeFirtTime of feeFirtTimes) {
    const feeFistTimeAmtTmp =
      feeFirtTime?.calcula_type == constant.FEE.TYPE_CALCULATE.FIXED_AMOUNT
        ? Number(feeFirtTime?.fee_amt || 0)
        : feeFirtTime?.calcula_type == constant.FEE.TYPE_CALCULATE.RECIPE
          ? common.roundUp(Number(feeFirtTime?.fee_amt || 0) * payload.amount, global.calcuCfg.scale)
          : 0
    feeFistTimeAmt += feeFistTimeAmtTmp
    feeDetailRepo.insertFeeDetail({
      ...payload,
      feeId: feeFirtTime.id,
      feeAmt: feeFistTimeAmtTmp
    })
  }
  payload.disbursementAmount -= feeFistTimeAmt
  return arrFees
}
async function insertIrCharge(payload) {
  const recordsIr = []
  for (const irChargeObj of payload.irCharge) {
    if (irChargeObj.irType == constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
      payload.irPrinOnDueRate = irChargeObj.irValue
    }
    recordsIr.push([
      payload.debtAckContractNumber,
      undefined,
      payload.productCode,
      irChargeObj.irCode,
      irChargeObj.irName,
      irChargeObj.irType,
      irChargeObj.irValue,
      constant.IR_CHARGE_STATUS.ACTIVE,
      payload.ownerId,
      payload.isTesting,
      payload.createdBy,
      irChargeObj.tenorFrom,
      irChargeObj.tenorTo,
      irChargeObj.installmentFrom,
      irChargeObj.installmentTo
    ])
  }
  irChargeRepo.insBatchIrCharge(recordsIr)
}

async function insertPromotion(payload, loanAccObj) {
  const { data } = installmentService.getEmiSimulation({
    startDate: moment().format(constant.DATE_FORMAT.YYYYMMDD2),
    tenor: loanAccObj.tenor,
    interestRate: Number(payload.irPrinOnDueRate),
    periodicity: Number(loanAccObj.periodicity),
    graceDayNumber: Number(loanAccObj.grace_day_number),
    aprLimitAmt: Number(loanAccObj.apr_limit_amt),
    listPromotionDeduction: []
  })
  const { promotionValue, promotionMaxValue, promotionType, installmentDeduction } = payload.promotion
  let totalPromotionAmount = promotionValue
  if (promotionType == constant.PROMOTION_TYPE.RATE) {
    totalPromotionAmount = Math.min((promotionValue / 100) * payload.voucher.voucherAmount, promotionMaxValue)
  }
  const totalDeductionAmount = Math.min(data.sumOriginalIrAmount, totalPromotionAmount)
  payload.totalPromotionAmount = totalPromotionAmount
  const revenueAmount = totalPromotionAmount - totalDeductionAmount
  promotionRepo.insertPromotion({
    ...payload.promotion,
    debtAckContractNumber: payload.debtAckContractNumber,
    deductionAmount: totalDeductionAmount,
    totalPromotionAmount: payload.totalPromotionAmount,
    revenueAmount
  })

  if (payload.totalPromotionAmount) {
    payload.disbursementAmount -= payload.totalPromotionAmount
  }
  if (Array.isArray(installmentDeduction)) {
    // const recordsInstallmentDeduction = installmentDeduction.map((item) => {
    //   const deductionAmount = common.roundUp(item.deductionRate * totalDeductionAmount, constant.CALCUCFG.scaleVoucher)
    //   return {
    //     debt_ack_contract_number: payload.debtAckContractNumber,
    //     promotion_code: payload?.promotion.promotionCode,
    //     num_cycle: item.numCycle,
    //     deduction_rate: item.deductionRate,
    //     deduction_amount: deductionAmount
    //   }
    // })
    // installmentDeductionRepo.insertManyDeductionInstallment(recordsInstallmentDeduction)
  }
}
/**
 * Tao khoan vay tren SMA
 * @param {*} req
 * @param {*} res
 */
const createDebtAckContractSMA = async function (payload) {
  try {
    // const payload = req.body
    common.log('req body createDebtAckContractSMA: ' + payload?.debtAckContractNumber)

    const validated = validateInputCreateKunn(payload)

    if (!validated.isSuccess) {
      return {
        code: 2,
        message: validated.message,
        statusCode: 400
      }
    }
    const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

    if (findLoanAcc.length) {
      return {
        code: 1,
        message: '[LMS-MC] debtAckContractNumber is existed',
        statusCode: 200
      }
    }
    payload.status = constant.DEBT_ACK_STATUS.SIG
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const listFunc = await Promise.all([
      mcLimitRepo.findActiveByContractNumberAndCustId(global.poolWrite, payload),
      debtAckContractRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolWrite, {
        contractNumber: payload.contractNumber
      }),
      loanAccountRepo.findFirstActiveKunnByContractNumberAndContractType(payload.contractNumber)
    ])
    const rsMclimit = listFunc[0]
    const rsLoanAcc = listFunc[1]

    if (rsMclimit.rowCount == 0) {
      return {
        code: 3,
        message: '[LMS-MC] Loan contract limit does not exist',
        statusCode: 200
      }
    }
    if (!isCashLoanPartnerCode(payload.partnerCode)) {
      let remainLimitAmount = Number(rsMclimit.rows[0].apr_limit_amt)
      for (const loanAccObj of rsLoanAcc.rows) {
        remainLimitAmount -= Number(loanAccObj.apr_limit_amt)
      }
      if (remainLimitAmount < payload.amount) {
        return {
          code: 4,
          message: '[LMS-MC] The remaining loan limit is not enough',
          data: { remainLimitAmount },
          statusCode: 200
        }
      }
    }
    const loanContractLimit = rsMclimit.rows[0]
    payload.custId = loanContractLimit.cust_id
    payload.contractLimitId = loanContractLimit.contract_limit_id
    payload.ccycd = payload.ccycd || 'VND'
    payload.graceDayNumber = payload.graceDayNumber || 0
    !payload.phoneNumber && (payload.phoneNumber = loanContractLimit.phone_number)
    const firstActiveKunn = listFunc[2]
    if (firstActiveKunn && payload.partnerCode == constant.PARTNER_CODE.MISA) {
      payload.billDay = firstActiveKunn.bill_day
      if (payload.contractType == constant.CONTRACT_TYPE.CASHLOAN) {
        payload.tenor = firstActiveKunn.tenor
      }
      const now = new Date()
      const differentDays = common.getDifferencesDays(firstActiveKunn.active_date, now)
      if (
        payload.contractType == constant.CONTRACT_TYPE.CASHLOAN &&
        differentDays > constant.MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN
      ) {
        return {
          code: 1,
          message: '[MC-LMS] Thoi gian tao KUNN lon hon 30 ngay so voi kunn 1',
          statusCode: 400
        }
      }
    }
    const rsIns = await debtAckContractRepo.insDebtAckContract(global.poolWrite, payload)
    const loanAccObj = rsIns.rows[0]

    // insert data vao bang ir charge
    for (const irChargeObj of payload.irCharge) {
      if (irChargeObj.irType == constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
        payload.irPrinOnDueRate = irChargeObj.irValue
      }
    }
    insertIrCharge(payload)

    if (isCashLoanPartnerCode(payload.partnerCode)) {
      if (payload.voucher) {
        voucherRepo.insertVoucher({
          ...payload.voucher,
          debtAckContractNumber: payload.debtAckContractNumber
        })
      }
      if (payload.promotion && payload.promotion?.promotionCode) {
        // insert khuyen mai
        insertPromotion(payload, loanAccObj)
      }
    }
    const arrFee = payload.fee
    insertFeesSMA(arrFee, payload)
    // call giai ngan khoan vay
    disbursementService.sendDisbursementRequest(payload, loanAccObj)

    return {
      code: 0,
      message: '[LMS-MC] Create contract successfully',
      data: camelcaseKeys(loanAccObj),
      statusCode: 200
    }
  } catch (error) {
    console.error('Error at createDebtAckContractSMA', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}
async function insertTransLogAndUpdateDisbursementStatus(payload, loanAccountObj) {
  const tranLogObj = await insTranLog(loanAccountObj, payload)
  const plUpdateDisbur = payload
  plUpdateDisbur.status = constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
  plUpdateDisbur.tranId = tranLogObj.tran_id
  plUpdateDisbur.partnerTranNo = payload.transactionId
  plUpdateDisbur.tranDate = common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
  // const rsDisbur = await disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur)
  const [rsDisbur, rsDisburWithDebtAck] = await Promise.all([
    disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur),
    disbursementRepo.updateStatusByDebtAck({
      ...plUpdateDisbur,
      debtAckContractNumber: loanAccountObj.debt_ack_contract_number
    })
  ])
  const disburseObj = {
    amt: 0,
    email: ''
  }
  if (rsDisbur.rowCount > 0) {
    disburseObj.email = rsDisbur.rows[0].email
    for (const e of rsDisbur.rows) {
      disburseObj.amt += Number(e.amt)
    }
  } else if (rsDisburWithDebtAck.rowCount > 0) {
    disburseObj.email = rsDisburWithDebtAck.rows[0].email
    for (const e of rsDisburWithDebtAck.rows) {
      disburseObj.amt += Number(e.amt)
    }
  }
  return disburseObj
}
async function insertTransLogAndUpdateDisbursementStatusV2(payload, loanAccObj) {
  // const tranLogObj = await insTranLog(loanAccountObj, payload)
  // const plUpdateDisbur = payload
  // plUpdateDisbur.status = constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
  // plUpdateDisbur.tranId = tranLogObj.tran_id
  // plUpdateDisbur.partnerTranNo = payload.transactionId
  // plUpdateDisbur.tranDate = common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
  // const rsDisbur = await disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur)
  const disBurListFunc = []
  if (!payload.disbursements || !Array.isArray(payload.disbursements)) {
    return {
      code: 2,
      message: '[LMS-MC] Update disbursement status fail',
      statusCode: 200
    }
  }
  const dateNow = moment().toDate()
  for (const disbur of payload.disbursements) {
    const tranLogIns = await tranLogRepo.insTranLog(global.poolWrite, {
      contractNumber: loanAccObj.contract_number,
      loanId: loanAccObj.loan_id,
      tranType: 'BANK',
      refId: disbur.transactionId,
      amtNumber: disbur.amount,
      tranDate: dateNow,
      valueDate: dateNow,
      tranDesc: 'Disbursement for ' + loanAccObj.debt_ack_contract_number,
      tranStatus: disbur.respCode,
      createdUser: constant.config.createdBy,
      sessionId: dateNow.getTime(),
      ownerId: constant.config.ownerId
    })
    const updatePayload = {
      ...payload,
      disburId: disbur.disburId,
      partnerCode: disbur.partnerCode,
      status:
        disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS
          ? constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
          : constant.DISBURSEMENT.TRANS_STATUS.DISBURMENT_IN_PROGESSING,
      tranId: tranLogIns?.rows?.[0].tran_id,
      partnerTranNo: disbur.transactionId,
      tranDate:
        disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS
          ? common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
          : null
    }
    if (disbur.isReDisburse && disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS) {
      updatePayload.reDisburseDate = common.convertDatetoString(
        disbur.reDisburseDate ? new Date(disbur.reDisburseDate) : new Date(),
        constant.DATE_FORMAT.YYYYMMDD
      )
    }
    disBurListFunc.push(disbursementRepo.updateStatusByDisburId(global.poolWrite, updatePayload))
  }
  return await Promise.all(disBurListFunc)
}

async function activeInsurance(debtAckContractNumber) {
  if (!debtAckContractNumber) {
    return {
      code: 1,
      message: '[MC-LMS] Invalid input',
      statusCode: 400
    }
  }
  const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

  if (!findLoanAcc.length) {
    return {
      code: 1,
      message: '[MC-LMS] Not found',
      statusCode: 400
    }
  }
  const loanAccount = findLoanAcc[0]

  await insuranceService.activeInsurances(loanAccount)

  return {
    code: 0,
    message: '[MC-LMS] Success',
    statusCode: 200
  }
}

async function getDetailInsurance(debtAckContractNumber) {
  if (!debtAckContractNumber) {
    return {
      code: 1,
      message: '[MC-LMS] Invalid input',
      statusCode: 400
    }
  }
  const loanAccount = await LoanAccountRepository.findOne({
    where: { debt_ack_contract_number: debtAckContractNumber }
  })

  if (!loanAccount) {
    return {
      code: 1,
      message: '[MC-LMS] Not found',
      statusCode: 400
    }
  }

  // const insurance = await InsuranceRepository.findOne({ where: { debt_ack_contract_number: debtAckContractNumber } })
  const insurances = await InsuranceRepository.findAll({
    where: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })

  if (!insurances || insurances?.length === 0) {
    return {
      code: 1,
      message: '[MC-LMS] Insurance Not found',
      statusCode: 400
    }
  }

  const insurancesWrapper = insurances.map(insurance => ({
    ...insurance,
    insur_rate: (insurance.insur_amt / insurance.amount) * 100
  }))

  return {
    code: 0,
    message: '[MC-LMS] Success',
    statusCode: 200,
    data: insurancesWrapper || []
  }
}

const getDisbursements = async (debtAckContractNumber) => {
  if (!debtAckContractNumber) {
    return {
      code: 1,
      message: '[MC-LMS] Invalid input',
      statusCode: 400
    }
  }
  const loanAccount = await LoanAccountRepository.findOne({
    where: { debt_ack_contract_number: debtAckContractNumber }
  })

  if (!loanAccount) {
    return {
      code: 1,
      message: '[MC-LMS] Not found',
      statusCode: 400
    }
  }

  const disburs = await DisbursementRepository.findAll({
    where: {
      debt_ack_contract_number: debtAckContractNumber
    },
    order: {
      created_date: 'DESC'
    }
  })

  if (!disburs || disburs?.length === 0) {
    return {
      code: 1,
      message: '[MC-LMS] Insurance Not found',
      statusCode: 400
    }
  }

  let newDisburs = [];
  for (const disbur of disburs) {
    let init = {
      debt_ack_contract_number: disbur.debt_ack_contract_number,
      bank_name: disbur.bank_name,
      bank_account: disbur.bank_account,
      tran_id: disbur.tran_id,
      amt: disbur.amt,
      tran_date: disbur.tran_date ? moment(disbur.tran_date).format(constant.DATE_FORMAT.YYYYMMDD2) : disbur.tran_date,
      partner_code: disbur.partner_code,
      receiver_name: disbur.receiver_name,
      partner_tran_no: disbur.partner_tran_no,
      tran_status: disbur.tran_status ? constant.DISBURSEMENT.TRANS_STATUS.codeToStatus[disbur.tran_status] : null,
      re_disburse_date: disbur.re_disburse_date ? moment(disbur.re_disburse_date).format(constant.DATE_FORMAT.YYYYMMDD2) : disbur.re_disburse_date,
      bank_code: disbur.bank_code ?? null
    }
    newDisburs.push(init);
  }

  return {
    code: 0,
    message: '[MC-LMS] Success',
    statusCode: 200,
    data: [...newDisburs]
  }
}

module.exports = {
  createDebtAckContractSMA,
  createDebtAckContract,
  activeDebtAckContract,
  refundSuspendFactoringDebtAckContract,
  callbackRefundSuspendDebtAckContract,
  transferSuspendFactoringDebtAckContract,
  getListLoanActive,
  activeInsurance,
  getDetailInsurance,
  activeDebtAckContractV2,
  activeBackdateDebtAckContract,
  updateDisburserment,
  insertTransLogAndUpdateDisbursementStatusV2,
  getDisbursements
}
