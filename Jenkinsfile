def registryCredential = 'harbor_user'
pipeline {
    agent any

    environment {
        APP_NAME = 'lms-mc-service'
        HELM_FOLDER = 'helm'
        HARBOR_URL = 'https://els-registry.evnfc.vn'
        SONAR_HOST_URL = "http://sonarqube.evnfc.vn"		
		projectKey = "els_easy-voucher_lms-mc-service_7d789ebc-74de-4ab6-8cca-be901be958b0"
        SONAR_TOKEN = "sqp_b2fec191107467f226168c13d8846b47361730dc"
    }


    stages {
        stage("Checkout code") {
            steps {
              checkout scm
            }
        }

        stage("Trivy FS Scan") {
            steps {
                sh '''
                trivy fs --severity CRITICAL,HIGH --ignore-unfixed --scanners vuln,license,secret  --format table .
                '''
            }
        }

        stage("Trivy Config Scan") {
            steps {
                sh '''
                trivy config --severity CRITICAL,HIGH --format table  .
                '''
            }
        }

     //   stage("SonarQube Scan") {
     //       steps {
     //               sh '''
     //               export PATH=$PATH:/opt/sonar-scanner-5.0.1.3006-linux/bin
     //               sonar-scanner \
     //                 -Dsonar.projectKey=${projectKey} \
     //                 -Dsonar.sources=. \
     //                 -Dsonar.host.url=${SONAR_HOST_URL} \
     //                 -Dsonar.login=${SONAR_TOKEN}
     //               '''
     //       }
     //   }
        // stage('Build image') {

        //     steps {
    
        //         script {
        //             imageId = "els-registry.evnfc.vn/els/${APP_NAME}:$BUILD_NUMBER"
        //             docker.withRegistry(HARBOR_URL, registryCredential) {
        //                 myapp = docker.build(imageId, '-f Dockerfile .')
        //                 myapp.push()
        //             }
        //         }
        //     }
        // }
        stage('Build image') {
            steps {
                script {
                    // Lấy tên branch hiện tại từ môi trường Jenkins
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')

                    // Tạo imageId với app name + branch name
                    imageId = "els-registry.evnfc.vn/els/${APP_NAME}-${branchName}:${BUILD_NUMBER}"

                    docker.withRegistry(HARBOR_URL, registryCredential) {
                        myapp = docker.build(imageId, '-f Dockerfile .')
                        myapp.push()
                    }
                }
            }
        }


        stage("Trivy Image Scan") {
            steps {
                script {
                    // Lấy tên branch hiện tại từ môi trường Jenkins
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')
                    def imageFull = "els-registry.evnfc.vn/els/${APP_NAME}-${branchName}:$BUILD_NUMBER"
                    sh """
                    trivy image --severity CRITICAL,HIGH --format table  ${imageFull}
                    """
                }
            }
        }
         stage('Deploy to server') {
            //when {
            //    environment name: 'GIT_BRANCH', value: 'origin/uat'
            //}

            steps {
                script {
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')
                    git branch: 'main',
                    credentialsId: 'user-els-gitlab-ssh',
                    url: '***********************:devops/config.git'

                sh """#!/bin/bash
                      git config --global user.email "<EMAIL>"
                      git config --global user.name "Jenkins"
                      git branch -a
                      cd services/els/${APP_NAME}
                      sed -i 's|tag: .*|tag: ${BUILD_NUMBER}|' values-${branchName}.yaml
                      git add .
                      git commit -m 'Deploy ${APP_NAME} version ${BUILD_NUMBER}'
                      git push origin main
                   """
                }
                
            }
        }
    }
}

